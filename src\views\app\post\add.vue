<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset class="item">
        <van-field
          v-model="form.title"
          label="岗位名称"
          placeholder="未设置"
          input-align="right"
          is-link
          :rules="[{ required: true, message: '请输入岗位名称' }]"
        />
      </van-cell-group>
      <van-cell-group inset class="item">
        <van-field
          v-model="form.personNames"
          label="人员"
          placeholder="未设置"
          input-align="right"
          is-link
          :rules="[{ required: true, message: '请输入人员' }]"
          readonly
          @click="headbutton"
        />
      </van-cell-group>
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit"
        >提交</van-button
      >
    </van-action-bar>
  </van-form>
</template>
      
    <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { useRouter } from "vue-router";
// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 表格数据
let form = ref({
  title: "",
  personNames: "",
});

// 选择人组件
const directorLsts = ref();
const headbutton = () => {
  let selectUsers = ref();

  if (directorLsts.value && directorLsts.value.length > 0) {
    selectUsers = directorLsts.value.map((item) => {
      return item.userid;
    });
  }

  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: true,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: selectUsers, //已选用户
    // pickedDepartments: selectDeparments.value, //已选部门
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      // 选择联系人或部门成功后的回调函数
      console.log(data);
      directorLsts.value = data.users.map((item) => {
        return { avatar: item.avatar, name: item.name, userid: item.emplId };
      });
      form.value.personNames = data.users.map((item) => item.name).join(",");
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
    },
  });
};

// 提交任务
const submit = () => {
  let temuserlst = {
    users: directorLsts.value,
    departments: [],
  };
  let postData = {
    title: form.value.title,
    userlst: JSON.stringify(temuserlst),
  };
  proxy
    .$post("position/post_add", postData)
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("提交成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
      
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
  padding-bottom: 100px;
}

.item {
  margin-top: 16px;
}

.imagestext {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;

  .imgItems {
    position: relative;
  }
  img {
    margin-right: 10px;
    width: 45px;
    height: 45px;
    border-radius: 3px;
  }

  img:first-child {
    margin-left: 15px;
  }

  .del {
    position: absolute;
    top: 2px;
    right: 11px;
    font-size: 12px;
    color: #b4b4b4;
  }
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>