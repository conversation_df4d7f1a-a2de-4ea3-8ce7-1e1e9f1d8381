<template>
  <div class="content">
    <van-cell-group inset>
      <van-field
        v-model="address"
        label="禅道地址"
        right-icon="arrow"
        placeholder="未设置"
        input-align="right"
      />
    </van-cell-group>
  </div>
  <van-action-bar class="bar">
    <van-button class="submit" type="primary" @click="submitUrl"
      >提交</van-button
    >
  </van-action-bar>
</template>

<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { showSuccessToast, showFailToast, closeToast } from "vant";

// 接口
const { proxy } = getCurrentInstance();

// 禅道地址
const address = ref("");

onBeforeMount(() => {
  // 获取禅道地址
  proxy
    .$get("link/get_info")
    .then((res) => {
      console.log(res);
      address.value = res.result.url;
    })
    .catch((err) => {
      console.log(err);
    });
});

// 提交修改禅道地址
const submitUrl = () => {
  proxy
    .$post("link/post_modify", {
      url: address.value,
    })
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("修改成功");
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>

<style lang="scss" scoped>
.content {
  padding: 16px 0;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>