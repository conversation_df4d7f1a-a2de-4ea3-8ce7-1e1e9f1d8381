<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset>
        <van-field
          v-model="title"
          label="分组名称"
          right-icon="arrow"
          placeholder="未设置"
          input-align="right"
          readonly
        />
      </van-cell-group>
    </div>
  </van-form>
</template>
    
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

// 分组名称
const title = ref("");

onMounted(() => {
  // console.log('--------------')
  // console.log(route.query);
  // 获取数据详情
  getTask(route.query);
});

// 获取详情
const getTask = (data) => {
  proxy
    .$get("group/get_info", data)
    .then((res) => {
      console.log(res);
      title.value = res.result.title;
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
    
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}

::v-deep .van-field__error-message {
  text-align: right;
}
</style>