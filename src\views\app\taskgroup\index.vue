<template>
  <div class="content">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="sortList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-if="sortList.length >= 1">
        <template v-for="(el, index) in sortList" :key="index">
          <div class="sort-item">
            <div class="title" @click="goDetail(el.id)">{{ el.title }}</div>
            <div class="icon" @click="showPopup(el)">
              <van-icon name="ellipsis" />
            </div>
          </div>
        </template>
      </template>
      <template v-else-if="showDesc && sortList.length == 0">
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </van-list>
  </div>
  <van-action-bar class="bar">
    <van-button class="add" type="primary" @click="goAdd()"
      >新增分组</van-button
    >
  </van-action-bar>
</template>
  
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRouter, onBeforeRouteUpdate } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 路由监听
watch(
  () => router.currentRoute.value.path,
  (newValue, oldValue) => {
    // console.log("watch", newValue, oldValue);
  },
  { immediate: true }
);

// 分类列表
let sortList = ref([]);
// 列表总数
let total = ref(0);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无内容");

onBeforeMount(() => {
  // getWorkSortData();
});

// 调动云一工作分类列表页
let config = ref({
  per_page: 10,
  page: 0,
});

const getWorkSortData = () => {
  proxy
    .$get("group/get_ls", config.value)
    .then((res) => {
      console.log(res.result);
      res.result.data.forEach((el) => {
        sortList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;
      // console.log(sortList);
      loading.value = false;
      // 加载完毕
      if (sortList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 添加
const goAdd = () => {
  router.push({ name: "TaskGroupAdd" });
};

// 详情
const goDetail = (id) => {
  router.push({ name: "TaskGroupDetail", query: { id: id } });
};

// 弹出层
const show = ref(false);
const operationTile = ref("");
const operationId = ref(0);
const actions = [
  { name: "修改", color: "#1678ff" },
  { name: "删除", color: "#1678ff" },
];
const showPopup = (el) => {
  proxy.$_dd.device.notification.actionSheet({
    title: el.title, //标题
    cancelButton: "取消", //取消按钮文本
    otherButtons: ["修改", "删除"],
    onSuccess: function (result) {
      console.log(result);
      if (result.buttonIndex == 0) {
        // 修改
        router.push({
          name: "TaskGroupEdit",
          query: { id: el.id },
        });
      } else if (result.buttonIndex == 1) {
        delData(el.id);
      }
      //onSuccess将在点击button之后回调
      /*{
            buttonIndex: 0 //被点击按钮的索引值，Number，从0开始, 取消按钮为-1
        }*/
    },
    onFail: function (err) {},
  });
};

// 删除
const delData = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除此分组么?",
  })
    .then(() => {
      proxy
        .$post("group/post_del", {
          id,
        })
        .then((res) => {
          console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("删除成功");
            config.value.page = 0;
            sortList.value = [];
            onLoad();
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    })
    .catch(() => {
      // on cancel
    });
};

const loading = ref(false);
const finished = ref(false);

// 列表加载
const onLoad = () => {
  console.log("加载数据");
  config.value.page = config.value.page + 1;
  getWorkSortData();
};
</script>
  
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
  padding-bottom: 80px;

  .sort-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5%;
    margin-top: 12px;
    padding: 0 16px;
    width: 90%;
    height: 54px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .icon {
      color: #c8c8c9;
    }
  }
}

.bar {
  height: 60px;

  .add {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}

.van-popup {
  div {
    text-align: center;
    height: 52px;
    line-height: 52px;
    font-size: 15px;
    color: #1678ff;
  }

  .line {
    border-bottom: 1px solid #eaebed;
  }

  .title {
    color: #a2a3a5;
  }

  .line-block {
    height: 12px;
    background-color: #f2f2f6;
  }

  .cancel {
    height: 55px;
    color: #000;
  }
}
</style>