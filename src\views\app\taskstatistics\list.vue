<template>
  <van-popup v-model:show="show" position="bottom">
    <van-date-picker
      v-model="currentDate"
      :title="datePickerTitle"
      @cancel="closeDate"
      @confirm="changeDate"
    />
  </van-popup>
  <div class="content" v-if="taskList.length >= 1">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="taskList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-for="(el, index) in taskList" :key="index">
        <div class="task-card" @click="goDetail(el.task_id)">
          <div class="header">
            <div class="title">{{ el.task_title }}</div>
            <div class="tag">
              <span v-if="el.status == 0" :style="{ color: '#959698' }"
                >待确认</span
              >
              <span v-else-if="el.status == 1" :style="{ color: '#ff662d' }"
                >待完成</span
              >
              <span v-else-if="el.status == 2" :style="{ color: '#00ba46' }"
                >已完成</span
              >
            </div>
          </div>
          <div class="desc">
            <van-row>
              <van-col span="8" class="verticalline">
                <div class="user">
                  <template v-if="el.avatar">
                    <img :src="el.avatar" alt="" />
                  </template>
                  <template v-else>
                    <div class="img">
                      {{ el.user_name[0] }}
                    </div>
                  </template>
                  <span>{{ el.user_name }}</span>
                </div>
              </van-col>
              <van-col span="8" class="verticalline">
                <div class="start" v-if="el.begin_time">
                  {{ el.begin_time }}
                </div>
                <div class="select" v-else>未选择时间</div>
              </van-col>
              <van-col span="8">
                <div class="end" v-if="el.end_time">
                  {{ el.end_time }}
                </div>
                <div class="select" v-else>未选择时间</div>
              </van-col>
            </van-row>
          </div>
        </div>
      </template>
    </van-list>
  </div>
  <div v-else>
    <van-empty
      image="/icons/acquiesce.png"
      image-size="250"
      :description="description"
    />
  </div>
</template>
  
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { useRoute, useRouter } from "vue-router";
const { proxy } = getCurrentInstance();

const route = useRoute();
const router = useRouter();

// 项目列表
let taskList = ref([]);
// 列表总数
let total = ref(0);

let description = ref("暂无内容");

// 列表加载
const loading = ref(false);
const finished = ref(false);

// 调动云一工作分类列表页
let config = ref({
  per_page: 10,
  page: 0,
  type: 0,
});

onMounted(() => {
  console.log(route.query);
  config.value.type = route.query.status;
  onLoad();
});

// 获取项目列表
const getList = () => {
  proxy
    .$get("task/get_my_ls", config.value)
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        res.result.data.forEach((el) => {
          taskList.value.push(el);
        });
        total.value = res.result.total;

        loading.value = false;
        // 加载完毕
        if (taskList.value.length >= total.value) {
          console.log("加载完毕 --->", taskList.value.length, total.value);
          finished.value = true;
        }
      } else {
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 点击跳转详情页
const goDetail = (id) => {
  console.log(id);
  router.push({
    name: "TaskStatisticsInfo",
    query: { id },
  });
};

// 列表加载
const onLoad = () => {
  // console.log("需要再次加载数据");
  config.value.page = config.value.page + 1;
  getList();
};
</script>
  
<style lang="scss" scoped>
.content {
  overflow: hidden;
}
.task-card {
  margin-left: 2%;
  margin-top: 12px;
  padding: 16px 12px;
  width: 96%;
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;

  .header {
    display: flex;
    justify-content: space-between;
  }

  .desc {
    margin-top: 17px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 13px;

    .verticalline {
      position: relative;
    }

    .verticalline::after {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: #eaebed;
    }
    .user {
      display: flex;
      align-items: center;
      img {
        display: block;
        width: 28px;
        height: 28px;
        border-radius: 6px;
      }
      .img {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        background-color: rgb(0, 137, 255);
        color: #fff;
        text-align: center;
        line-height: 28px;
      }
      span {
        margin-left: 8px;
      }
    }

    .select {
      color: #a2a3a5;
    }
  }
}
</style>