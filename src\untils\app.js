import { useLoginStore } from "@/store/dingLogin";
import { config } from "@/config/router";
export function setTransitionName(to, from) {
  let toLg = to.path.split("/").length;
  let fromLg = from.path.split("/").length;
  to.meta.transitionName =
    toLg > fromLg ? "left" : toLg === fromLg ? "fade" : "right";
}
export function checkRouter(to, from) {
  if (config.whiteList.includes(to.path)) {
    return true;
  }
  let app = useLoginStore();
  return app.token
}
