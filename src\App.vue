<template>
  <router-view v-slot="{ Component }">
    <transition :name="route.meta.transitionName">
      <component :is="Component" />
    </transition>
  </router-view>
  <van-tabbar route v-if="isShowTabbar" class="tabs">
    <van-tabbar-item replace to="/index">
      <span>首页</span>
      <template #icon="props">
        <img
          :src="
            props.active ? '/icons/activeindex.png' : '/icons/inactiveindex.png'
          "
          alt=""
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item replace to="/app">
      <span>应用</span>
      <template #icon="props">
        <img
          :src="
            props.active ? '/icons/activeapp.png' : '/icons/inactiveapp.png'
          "
          alt=""
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item replace to="/mine">
      <span>我的</span>
      <template #icon="props">
        <img
          :src="
            props.active ? '/icons/activemine.png' : '/icons/inactivemine.png'
          "
          alt=""
        />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, watch, ref, provide } from "vue";
import { start, close } from "@/untils/nprogress";
import { useLoginStore } from "@/store/dingLogin";

// echart
provide("echarts", echarts);

// 是否显示tabbar
const isShowTabbar = ref(true);
// 是否显示应用
let showApp = ref(true);

let route = useRoute();
start();
onMounted(() => {
  close();

  window.onload = function () {
    document.addEventListener("touchstart", function (event) {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    });
    document.addEventListener("gesturestart", function (event) {
      event.preventDefault();
    });
  };
  // const permission = useLoginStore().loginData.permission;
  // console.log("权限->", permission);
  // if (permission.actions.length == 0) {
  //   showApp.value = false;
  // }
});
// 监听路由来动态控制tabbar的显示与隐藏
watch(
  () => route.path,
  (to, from) => {
    const pathArr = ["/index", "/app", "/mine"];
    if (pathArr.includes(to)) {
      isShowTabbar.value = true;
    } else {
      isShowTabbar.value = false;
    }
  }
);
</script>

<style lang="scss">
@use "./assets/styles/init.scss";
@use "./assets/styles/transition.scss";

* {
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  user-select: none;
}

#app {
  width: 100%;
  min-height: 100vh;
  background: rgb(242, 243, 244);
}

// .tabs {
//   height: 75px !important;
// }
</style>
