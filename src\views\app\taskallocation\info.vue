<template>
  <div class="content" ref="content">
    <div class="title">{{ taskDetail.title }}</div>
    <div class="desc">{{ taskDetail.content }}</div>
    <div class="file">
      <template v-for="(el, x) in taskDetail.files" :key="x">
        <div class="fileItem" @click="showFile(x)">
          <template
            v-if="
              el.url.match(/.{4}$/)[0] == 'jpeg' ||
              el.url.match(/.{3}$/)[0] == 'MOV' ||
              el.url.match(/.{3}$/)[0] == 'jpg'
            "
          >
            <img :src="el.url" alt="" />
          </template>
          <template v-else>
            <img src="/icons/Excel.png" alt="" />
          </template>
          <a :href="el" class="text">{{ el.name }}</a>
        </div>
      </template>
    </div>
    <div class="buts">
      <div class="user but">
        <van-icon name="contact" class="icon" />{{ taskDetail.users }}
      </div>
      <div
        class="end but"
        :style="{ color: taskDetail.publish_time ? '#000' : '#a2a3a5' }"
      >
        <van-icon name="notes-o" class="icon" />{{
          taskDetail.publish_time ? taskDetail.publish_time : "上线时间"
        }}
      </div>
      <div class="sort but">
        <van-icon name="label-o" class="icon" />任务分组:
        {{ taskDetail.group_title }}
      </div>
    </div>
    <div class="task-flow">
      <div class="title">任务流程</div>
      <van-divider />
      <template v-for="(item, index) in taskDetail.process" :key="index">
        <p>{{ item.showData }} {{ item.user_name }} {{ item.action }}</p>
      </template>
      <template v-if="taskDetail.status == 0">
        <div class="btns">
          <div class="bt sblue" @click="overTask" v-if="ISjurisdiction">
            <van-icon name="passed" class="icon" />
            <span>结束任务</span>
          </div>
          <div class="bt sred" @click="reProject">
            <van-icon name="close" class="icon" />
            <span>重新立项</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="btn swhite">
          <img class="finish" src="/icons/finish.png" alt="" />
          <span>{{ taskDetail.status == 1 ? "已完成" : "已废弃" }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  showImagePreview,
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { ISjurisdiction } from "../../../untils/commit";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 记录视口高度
const viewportHeight = ref(0);
const content = ref(null);

// 详情数据信息
let taskDetail = ref({});
let is_operate = ref();
// 发送数据
let PostData = ref();
let fileList = ref([]);

onMounted(() => {
  // console.log(route.query);
  PostData.value = {
    id: route.query.id,
  };
  is_operate.value = route.query.is_operate;
  // 获取详情数据
  getTaskDetailData(PostData.value);
});

// 获取详情数据
const getTaskDetailData = (postData) => {
  proxy
    .$get("task/get_info", postData)
    .then((res) => {
      // console.log(res.result);
      for (let i in res.result) {
        let item = res.result[i];
        // console.log("-----------------------");
        // console.log(item);
        if (i == "flow") {
          res.result.users = `${item.title}，${item.finish}/${item.total}已完成`;
        } else if (i == "publish_time" && item) {
          const dataArr = item.split("-");
          res.result.publish_time = `${dataArr[1]}月${dataArr[2]}日 上线`;
        } else if (i == "process") {
          // console.log(item);
          item.forEach((el) => {
            el.data = el.created_at.split(" ");
            el.yearData = el.data[0].split("-");
            el.dayData = el.data[1].split(":");
            el.showData = `${el.yearData[1]}月${el.yearData[2]}日 ${el.dayData[0]}: ${el.dayData[1]}`;
          });
        } else if (i == "file") {
          if (res.result.files) {
            res.result.files = JSON.parse(item);
            res.result.files.forEach((el) => {
              fileList.value.push(el.url);
            });
          }
        }
      }
      taskDetail.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 结束任务
const overTask = () => {
  showConfirmDialog({
    title: "结束任务",
    message: "确定结束该任务么?",
  })
    .then(() => {
      // console.log("-----------------");
      proxy
        .$post("task/post_finish", PostData.value)
        .then((res) => {
          if (res.errcode == 0) {
            showSuccessToast("任务已结束");
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
      setTimeout(() => {
        closeToast();
        router.go(-1);
      }, 1000);
    })
    .catch(() => {
      // on cancel
    });
};
// 废弃任务
const reProject = () => {
  showConfirmDialog({
    title: "重新立项",
    message: "确定废弃该任务么?",
  })
    .then(() => {
      // console.log("-----------------");
      proxy
        .$post("task/post_re_project", PostData.value)
        .then((res) => {
          if (res.errcode == 0) {
            showSuccessToast("任务已废弃");
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
      setTimeout(() => {
        closeToast();
        router.push({ name: "TaskAllocationAdd" });
      }, 1000);
    })
    .catch(() => {
      // on cancel
    });
};

// 展示文件
const showFile = (index) => {
  showImagePreview({
    images: fileList.value,
    startPosition: index,
  });
};
</script>

<style lang="scss" scoped>
.content {
  padding: 26px;
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  box-sizing: border-box;
}

.title {
  font-size: 17px;
  font-weight: 550;
  line-height: 22px;
}

.desc {
  margin-top: 26px;
  font-size: 15px;
  line-height: 22px;
  color: #747677;
}

.file {
  margin-top: 26px;
  .fileItem {
    margin-top: 16px;
    display: flex;
    padding: 6px 8px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: #ebf5ff;

    img {
      margin-right: 8px;
      width: 45px;
      height: 45px;
      border-radius: 4px;
    }

    .text {
      color: #171a1d;
      margin-left: 5%;
      width: 60%;
      line-height: 45px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.buts {
  margin-top: 46px;
  .but {
    display: inline-block;
    margin-right: 40px;
    margin-top: 12px;
    padding: 0 16px;
    border: 1px solid #eaebed;
    border-radius: 17px;
    line-height: 34px;

    .icon {
      margin-right: 6px;
    }
  }

  .start {
    margin-right: 26px;
  }
}

.task-flow {
  margin-top: 66px;

  p {
    text-align: center;
    color: #a2a3a5;
  }

  .btn {
    position: fixed;
    left: 50%;
    bottom: 70px;
    transform: translateX(-50%);
    margin-top: 32px;
    padding: 13px;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    border-radius: 24px;
    display: inline-block;

    .icon {
      margin-right: 8px;
    }
  }

  .sblue {
    background-color: #007fff;
    color: #fff;
  }
  .sred {
    background-color: #ff642b;
    color: #fff;
  }

  .btns {
    position: fixed;
    left: 0;
    bottom: 70px;
    display: flex;
    justify-content: space-around;
    width: 100%;
    .bt {
      padding: 13px;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      border-radius: 24px;
      display: inline-block;
      .icon {
        margin-right: 8px;
      }
    }
  }

  .swhite {
    padding: 13px;
    display: flex;
    background-color: #000;
    color: #fff;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    z-index: 10;
    line-height: 24px;

    .finish {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }
  }
}
</style>