<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset>
        <van-field
          v-model="form.title"
          label="任务流名称"
          placeholder="未设置"
          input-align="right"
          is-link
        />
      </van-cell-group>
      <template v-for="(item, index) in form.process" :key="index">
        <div class="step-item">
          <span
            :style="{
              color: '#9a9c9e',
            }"
            >第{{ toChinesNum(index + 1) }}步</span
          >
          <span
            :style="{
              color: '#007fff',
            }"
            @click="delFlowItem(index)"
            >删除</span
          >
        </div>
        <van-cell-group inset>
          <van-field
            v-model="item.title"
            label="名称"
            placeholder="未设置"
            input-align="right"
            is-link
          />
          <van-field
            v-model="form.userlst"
            label="人员"
            readonly
            @click="headbutton(index)"
          >
            <template #right-icon>
              <van-icon name="add-o" />
            </template>
          </van-field>
          <template v-if="item.user">
            <div class="imagestext">
              <template v-for="(el, inx) in item.user" :key="inx">
                <div class="imgItems" @click="delUser(index, inx)">
                  <img :src="el.avatar" alt="" />
                  <div class="name">{{ el.name }}</div>
                  <van-icon
                    class="del"
                    name="cross"
                    :style="{ color: '#bebec2', marginLeft: '13px' }"
                  />
                </div>
              </template>
            </div>
          </template>
        </van-cell-group>
      </template>
      <van-cell-group inset class="add-step">
        <van-field
          class="add-step-icon"
          readonly
          left-icon="add-o"
          @click="addTaskFlow"
        >
          <template #label> 新增流程 </template>
        </van-field>
      </van-cell-group>
      <!-- <div class="step-item">
          <span
            :style="{
              color: '#9a9c9e',
            }"
            >任务流规则</span
          >
        </div>
        <van-cell-group inset>
          <van-field
            v-model="flowTypeText"
            is-link
            readonly
            label="运行方式"
            placeholder="未设置"
            input-align="right"
            @click="showPicker = true"
          />
          <van-popup
            v-model:show="showPicker"
            closeable
            close-icon-position="top-left"
            position="bottom"
            round
            :style="{ height: '30%' }"
            class="van-popup"
          >
            <div class="header">运行方式</div>
            <div class="options">
              <van-divider />
              <template v-for="(el, inx) in typeColumn" :key="inx">
                <div class="p" @click="switchFlowType(el)">
                  <div>{{ el.text }}</div>
                  <img
                    src="/icons/select.png"
                    alt=""
                    v-if="form.flowType == el.value"
                  />
                </div>
                <van-divider />
              </template>
            </div>
          </van-popup>
        </van-cell-group> -->
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit"
        >提交</van-button
      >
    </van-action-bar>
  </van-form>
</template>
      
    <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { showToast, showSuccessToast, showFailToast, closeToast  } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { useRoute, useRouter } from "vue-router";

// 路由跳转
const route = useRoute();
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 表格数据
let form = ref({
  title: "",
  process: [
    {
      title: "",
    },
  ],
  // flowType: undefined,
});

onMounted(() => {
  console.log(route.query);
  form.value.id = route.query.id;
  // 获取数据详情
  getTask(route.query);
});

// 获取详情
const getTask = (data) => {
  proxy
    .$get("flow/get_info", data)
    .then((res) => {
      // console.log(res.result);
      form.value.title = res.result.title;
      form.value.id = res.result.id;
      const getProcess = JSON.parse(res.result.process);
      getProcess.forEach((el) => {
        el.user = el.userlst.users;
      });
      // console.log(getProcess);
      form.value.process = getProcess;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 选择人组件
// const directorLsts = ref();
const headbutton = (index) => {
  let selectUsers = ref();
  console.log(form.value.process[index].user);
  if (form.value.process[index].user) {
    let directorLsts = form.value.process[index].user;
    console.log(directorLsts);
    selectUsers = directorLsts.map((item) => {
      return item.userid;
    });
  }

  // console.log(selectUsers);
  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: true,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: selectUsers, //已选用户
    // pickedDepartments: selectDeparments.value, //已选部门
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      // 选择联系人或部门成功后的回调函数
      const postUser = data.users.map((item) => {
        return { avatar: item.avatar, name: item.name, userid: item.emplId };
      });
      console.log(postUser);
      form.value.process[index].user = postUser;
      console.log(form.value.process[index].user);
      console.log("-----------------");

      // form.value.process[index].userlst = postUser;
      //   directorLsts.value = data.users.map((item) => item.name).join(",");
      //   selectUsers.value = data.users.map((item) => item.emplId);
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
    },
  });
};

// 删除选人
const delUser = (index, inx) => {
  let directorLsts = form.value.process[index].user;
  directorLsts.splice(inx, 1);
};

// // 方式选择
// const typeColumn = [
//   { text: "并行", value: 0 },
//   { text: "串行", value: 1 },
// ];
// const showPicker = ref(false);
// const flowTypeText = ref("");

// // 点击选项
// const switchFlowType = (item) => {
//   // console.log(item);
//   form.flowType = item.value;
//   flowTypeText.value = item.text;
//   showPicker.value = false;
// };

// 提交表单
const submit = () => {
  console.log(form.value);
  let postProcess = [];
  form.value.process.forEach((el) => {
    let postProcessItem = {
      title: el.title,
      userlst: {
        users: el.user,
        departments: [],
      },
    };
    postProcess.push(postProcessItem);
  });
  proxy
    .$post("flow/post_modify", {
      id: form.value.id,
      title: form.value.title,
      process: JSON.stringify(postProcess),
    })
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("修改成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 新增任务流
const addTaskFlow = ({}) => {
  console.log("新增任务");
  const taskFlowItem = {
    title: "",
  };
  form.value.process.push(taskFlowItem);
};

// 删除任务流
const delFlowItem = (index) => {
  console.log(index);
  form.value.process.splice(index, 1);
};

//数字转中文数字
const toChinesNum = (num) => {
  let changeNum = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  let unit = ["", "十", "百", "千", "万"];
  num = parseInt(num);
  let getWan = (temp) => {
    let strArr = temp.toString().split("").reverse();
    let newNum = "";
    for (let i = 0; i < strArr.length; i++) {
      newNum =
        (i === 0 && strArr[i] === 0
          ? ""
          : i > 0 && strArr[i] === 0 && strArr[i - 1] === 0
          ? ""
          : changeNum[strArr[i]] + (strArr[i] === 0 ? unit[0] : unit[i])) +
        newNum;
    }
    return newNum;
  };
  let overWan = Math.floor(num / 10000);
  let noWan = num % 10000;
  if (noWan.toString().length < 4) {
    noWan = "0" + noWan;
  }
  return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
};
</script>
      
  <style lang="scss" scoped>
.content {
  padding-top: 16px;
  padding-bottom: 100px;
}
.step-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  width: 90%;
  margin-left: 5%;
  margin-top: 25px;
  margin-bottom: 12px;
}

.imagestext {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  margin-left: 4%;

  .imgItems {
    display: flex;
    background-color: #f2f2f6;
    line-height: 24px;
    padding: 6px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 14px;

    img {
      margin-right: 8px;
      width: 24px;
      height: 24px;
      border-radius: 3px;
    }
    .del {
      line-height: 24px;
    }
  }
}

// .options {
//   display: flex;
//   justify-content: space-between;
//   width: 90%;
// }

.van-popup {
  .header {
    margin-top: 14px;
    text-align: center;
    font-size: 17px;
    line-height: 25px;
  }

  .p {
    display: flex;
    justify-content: space-between;
    width: 86%;
    margin-left: 7%;

    img {
      width: 14px;
      height: 10px;
    }
  }
}

.add-step {
  margin-top: 16px;
}

.add-step ::v-deep .van-field__label {
  color: #007fff;
}

.add-step-icon {
  color: #007fff;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>