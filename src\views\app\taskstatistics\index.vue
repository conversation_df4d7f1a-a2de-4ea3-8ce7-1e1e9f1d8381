<template>
  <!-- <div v-if="errCode==510"> -->
    <template v-if="errcode==510">
    <van-empty
      image="/icons/acquiesce.png"
      image-size="250"
      description="权限不足"
    />
  </template>
  <!-- </div> -->
  <div class="content">
    <div class="summary card">
      <van-row>
        <van-col span="12">
          <div class="left" @click="goTaskList(3)">
            <div class="l1">{{ summary.total }}</div>
            <div class="l2">全部工作数</div>
          </div>
        </van-col>
        <van-col span="12">
          <div class="right" @click="goTaskList(4)">
            <div class="l1">{{ summary.todo }}</div>
            <div class="l2">未完成工作数</div>
          </div>
        </van-col>
      </van-row>
    </div>
    <div class="vertical-table card">
      <div id="vertical" class="card-content"></div>
    </div>
    <div class="horizontal-table card">
      <div id="horizontal" class="card-content"></div>
    </div>
    <div class="todo-table card">
      <div id="todo" class="card-content"></div>
    </div>
    <div class="type-table card">
      <div id="type" class="card-content"></div>
    </div>
    <div class="curve-table card">
      <div id="curve" class="card-content"></div>
    </div>
  </div>
</template>
  
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  inject,
  defineComponent,
  getCurrentInstance,
  watch,
} from "vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
let route = useRoute();

const { proxy } = getCurrentInstance();

let summary = ref({});
let echarts = inject("echarts"); // 主要
let verticalData = ref({
  name: [],
  todo: [],
  finish: [],
});
let horizontalData = ref({
  name: [],
  todo: [],
});
let todoData = ref([]);
let typeData = ref([]);
let curveData = ref({
  todo: [],
  finish: [],
});
let errcode= ref(0);
watch(
  () => route.path,
  (to, from) => {
    console.log("----路由监听-----");
    location.reload();
    // 获取数据
    getTotal();
    getVertical();
    gethorizontal();
    getTodo();
    getType();
    getCurve();
  }
);

onMounted(() => {
  // console.log("加载了么111");
  // 获取数据
  getTotal();
  getVertical();
  gethorizontal();
  getTodo();
  getType();
  getCurve();
});

// 跳转至任务列表
const goTaskList = (index) => {
  // router.push({
  //   name: "TaskStatisticsList",
  //   query: { status: index },
  // });
};

// 获取数据总览
const getTotal = () => {
  proxy
    .$get("datav/get_overview")
    .then((res) => {
      // console.log(res.result);
      summary.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取工作数表
const getVertical = () => {
  proxy
    .$get("datav/get_count_table")
    .then((res) => {
      // console.log(res.result);
      res.result.forEach((el) => {
        verticalData.value.name.push(el.name);
        verticalData.value.todo.push(el.todo);
        verticalData.value.finish.push(el.finish);
      });
      vertical();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取未完成清单
const gethorizontal = () => {
  proxy
    .$get("datav/get_todo_table")
    .then((res) => {
      // console.log(res.result);
      res.result.forEach((el) => {
        horizontalData.value.name.push(el.name);
        horizontalData.value.todo.push(el.todo);
      });
      horizontal();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取剩余工作分布图
const getTodo = () => {
  proxy
    .$get("datav/get_residue")
    .then((res) => {
      // console.log(res.result);
      todoData.value = res.result;
      todo();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取任务类型分布图
const getType = () => {
  proxy
    .$get("datav/get_group")
    .then((res) => {
      // console.log(res.result);
      typeData.value = res.result;
      type();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取工作数表
const getCurve = () => {
  proxy
    .$get("datav/get_week_data")
    .then((res) => {
      errcode.value=res.errcode;
      // console.log(res.result);
      res.result.forEach((el) => {
        curveData.value.todo.push(el.todo);
        curveData.value.finish.push(el.finish);
      });
      curve();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 工作数表
const vertical = () => {
  console.log("再次加载了么7/4");
  const vertical = echarts.init(document.getElementById("vertical")); // 主要
  const option = {
    title: {
      text: "工作数表",
      textStyle: {
        fontSize: 15,
        lineHeight: 22,
      },
    },
    // 两个按钮的样式
    legend: {
      // 对应 series里的 name
      data: ["未完成", "已完成"],
      // 距离底部
      bottom: 10,
      //   // 字体样式
      //   textStyle: {
      //     color: "#A8AAB3", // 坐标值得具体的颜色
      //     fontSize: 12,
      //   },
    },
    // tooltip: {
    //   trigger: "axis",
    //   axisPointer: {
    //     type: "shadow",
    //   },
    // },
    xAxis: {
      // data: [
      //   "徐星辰",
      //   "王佳文",
      //   "李甜",
      //   "郭红旗",
      //   "解说性",
      //   "赵世泰",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      // ],
      data: verticalData.value.name,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
    },
    dataZoom: [
      {
        type: "inside",
        startValue: 1,
        endValue: 5,
        show: true,
      },
    ],
    series: [
      {
        name: "未完成",
        type: "bar",
        // data: [17, 3, 13, 17, 13, 9, 4, 23, 4, 1, 4, 9],
        data: verticalData.value.todo,
        label: {
          show: true,
          position: "top",
          color: "#9e9fa0",
          borderRadius: 10,
        },
      },
      {
        name: "已完成",
        type: "bar",
        // data: [12, 8, 3, 9, 12, 4, 4, 1, 2, 3, 1, 0],
        data: verticalData.value.finish,
        label: {
          show: true,
          position: "top",
          color: "#9e9fa0",
        },
      },
    ],
    color: ["#2fdc77", "#3052f1"],
  };
  vertical.setOption(option);
  // 根据页面大小自动响应图表大小
  window.addEventListener("resize", function () {
    vertical.resize();
  });
};
// 未完成清单
const horizontal = () => {
  const horizontal = echarts.init(document.getElementById("horizontal")); // 主要
  const option = {
    title: {
      text: "未完成清单",
      textStyle: {
        fontSize: 15,
        lineHeight: 22,
      },
    },
    // tooltip: {
    //   trigger: "axis",
    //   axisPointer: {
    //     type: "shadow",
    //   },
    // },
    xAxis: {},
    yAxis: {
      // data: [
      //   "徐星辰",
      //   "王佳文",
      //   "李甜",
      //   "郭红旗",
      //   "解说性",
      //   "赵世泰",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      //   "于东洋",
      // ],
      data:
        horizontalData.value.name.length == 0
          ? ["暂无"]
          : horizontalData.value.name,
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        overflow: "truncate",
        align: "right",
        fontSize: 9,
        interval: 0, //横轴信息全部显示
      },
    },
    dataZoom: [
      {
        type: "inside",
        startValue: 1,
        endValue: 5,
        show: true,
        orient: "vertical",
      },
    ],
    series: [
      {
        type: "bar",
        // data: [17, 3, 13, 17, 13, 9, 4, 21, 13, 9, 3, 7],
        data:
          horizontalData.value.todo.length == 0
            ? [0]
            : horizontalData.value.todo,
        label: {
          show: true,
          position: "right",
          color: "#9e9fa0",
        },
      },
    ],
    color: ["#2fdc77", "#3052f1"],
  };
  horizontal.setOption(option);
  // 根据页面大小自动响应图表大小
  // window.addEventListener("resize", function () {
  //   horizontal.resize();
  // });
};
// 剩余工作分布图
const todo = () => {
  const todo = echarts.init(document.getElementById("todo")); // 主要
  const option = {
    title: {
      text: "剩余工作分布图",
      textStyle: {
        fontSize: 15,
        lineHeight: 22,
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} ({d}%)",
    },
    legend: {
      bottom: 10,
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["30%", "70%"],
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          normal: {
            position: "inside",
            show: true,
            color: "#fff",
            formatter: (d) => {
              return d.percent + "%";
            },
          },
        },
        // data: [
        //   { value: 1048, name: "Search Engine" },
        //   { value: 735, name: "Direct" },
        //   { value: 580, name: "Email" },
        //   { value: 484, name: "Union Ads" },
        //   { value: 300, name: "Video Ads" },
        // ],
        data:
          todoData.value.length == 0
            ? [{ value: 0, name: "暂无" }]
            : todoData.value,
      },
      // {
      //   name: "Access From",
      //   type: "pie",
      //   radius: ["30%", "50%"],
      //   itemStyle: {
      //     borderColor: "#fff",
      //     borderWidth: 2,
      //   },
      //   // data: [
      //   //   { value: 1048, name: "Search Engine" },
      //   //   { value: 735, name: "Direct" },
      //   //   { value: 580, name: "Email" },
      //   //   { value: 484, name: "Union Ads" },
      //   //   { value: 300, name: "Video Ads" },
      //   // ],
      //   data:
      //     todoData.value.length == 0
      //       ? [{ value: 0, name: "暂无" }]
      //       : todoData.value,
      // },
    ],
  };
  todo.setOption(option);
};
// 任务类型分布图
const type = () => {
  const type = echarts.init(document.getElementById("type")); // 主要
  const option = {
    title: {
      text: "任务类型分布图",
      textStyle: {
        fontSize: 15,
        lineHeight: 22,
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} ({d}%)",
    },
    legend: {
      bottom: 10,
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["30%", "70%"],
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          normal: {
            position: "inside",
            show: true,
            color: "#fff",
            formatter: (d) => {
              return d.percent + "%";
            },
          },
        },
        // data: [
        //   { value: 1048, name: "Search Engine" },
        //   { value: 735, name: "Direct" },
        //   { value: 580, name: "Email" },
        //   { value: 484, name: "Union Ads" },
        //   { value: 300, name: "Video Ads" },
        // ],
        data:
          typeData.value.length == 0
            ? [{ value: 0, name: "暂无" }]
            : typeData.value,
      },
      // {
      //   name: "Access From",
      //   type: "pie",
      //   radius: ["30%", "50%"],
      //   itemStyle: {
      //     borderColor: "#fff",
      //     borderWidth: 2,
      //   },
      //   data:
      //     typeData.value.length == 0
      //       ? [{ value: 0, name: "暂无" }]
      //       : typeData.value,
      //   // data: [
      //   //   { value: 1048, name: "Search Engine" },
      //   //   { value: 735, name: "Direct" },
      //   //   { value: 580, name: "Email" },
      //   //   { value: 484, name: "Union Ads" },
      //   //   { value: 300, name: "Video Ads" },
      //   // ],
      // },
    ],
  };
  type.setOption(option);
};
// 工作数表
const curve = () => {
  const curve = echarts.init(document.getElementById("curve")); // 主要
  const option = {
    title: {
      text: "工作数表",
      textStyle: {
        fontSize: 15,
        lineHeight: 22,
      },
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      bottom: 10,
    },
    xAxis: {
      type: "category",
      data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      // type: "value",
    },
    series: [
      {
        name: "已完成",
        // data: [82, 93, 90, 93, 129, 133, 132],
        data: curveData.value.finish,
        smooth: true,
        type: "line",
      },
      {
        name: "未完成",
        // data: [23, 53, 35, 34, 93, 67, 23],
        data: curveData.value.todo,
        smooth: true,
        type: "line",
      },
    ],
  };
  curve.setOption(option);
};
</script>

  
<style lang="scss" scoped>
.content {
  padding: 16px 10px;
  overflow: hidden;
}

.card {
  // width: 94%;
  // margin:0 3%;
  background-color: #fff;
  border-radius: 8px;
}

.summary {
  padding: 26px 0;
  box-sizing: border-box;
  text-align: center;

  .l1 {
    font-size: 20px;
    line-height: 22px;
  }

  .l2 {
    margin-top: 8px;
    font-size: 13px;
    line-height: 22px;
    color: #747677;
  }

  .left {
    position: relative;
  }

  .left::after {
    content: "";
    display: block;
    position: absolute;
    top: 13px;
    right: 0;
    width: 1px;
    height: 26px;
    background-color: #eaebed;
  }
}
.vertical-table,
.horizontal-table,
.curve-table,
.todo-table,
.type-table {
  margin-top: 12px;
  // height: 265px;
  padding-top: 12px;
  padding-left: 16px;
  box-sizing: border-box;
  overflow: scroll;
}

.card-content {
  width: 100%;
  height: 15rem;
  // max-height: 500px;
}
// .todo-table,
// .type-table {
//   margin-top: 12px;
//   // height: 348px;
//   // padding: 12px 16px;
//   box-sizing: border-box;
// }

#todo,
#type {
  // width: 348px;
  height: 25rem;
}
</style>