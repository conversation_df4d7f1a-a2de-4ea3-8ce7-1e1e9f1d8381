<template>
  <div class="header">
    <div class="left">
      <div class="img">
        <img :src="userInfo.avatar" alt="" />
      </div>
      <div class="desc">
        <div class="l1">
          <span class="name">{{ userInfo.name }}</span>
          <!-- <span class="identity">{{ userInfo.sort }}</span> -->
        </div>
        <div class="l2">{{ userInfo.department_name }}</div>
      </div>
    </div>
    <!-- <div class="right" @click="switchID">切换身份</div> -->
  </div>
  <div class="btns">
    <van-cell-group inset>
      <van-cell
        title="产品咨询"
        is-link
        icon="service-o"
        @click="goBtnPage('service')"
      />
    </van-cell-group>
    <van-cell-group inset :style="{ marginTop: '16px' }">
      <van-cell
        title="任务统计"
        is-link
        icon="bar-chart-o"
        @click="goBtnPage('task')"
      />
    </van-cell-group>
    <van-cell-group inset :style="{ marginTop: '16px' }">
      <template v-for="(el, index) in btns" :key="index">
        <!-- <div class="btn" @click="goBtnPage(el.goName)">
        <van-icon class="licon" :name="el.icon" />
        <span>{{ el.title }}</span>
        <van-icon class="ricon" name="arrow" />
      </div> -->
        <van-cell
          :title="el.title"
          is-link
          :icon="el.icon"
          @click="goBtnPage(el.goName)"
        />
      </template>
    </van-cell-group>
  </div>
  <van-popup
    v-model:show="showPicker"
    closeable
    close-icon-position="top-left"
    position="bottom"
    round
    :style="{ height: '30%' }"
    class="van-popup"
  >
    <div class="headers">切换身份</div>
    <div class="options">
      <van-divider />
      <template v-for="(el, inx) in typeColumn" :key="inx">
        <div class="p" @click="switchFlowType(el)">
          <div>{{ el.text }}</div>
          <img
            src="/icons/select.png"
            alt=""
            v-if="flowTypeValue == el.value"
          />
        </div>
        <van-divider />
      </template>
    </div>
  </van-popup>
</template>

<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRouter } from "vue-router";
import { useLoginStore } from "@/store/dingLogin";
console.log("---------------");
console.log(useLoginStore().loginData.user_info);

// 接口
const { proxy } = getCurrentInstance();

// 用户信息
let userInfo = ref({
  img: "https://static-legacy.dingtalk.com/media/lADPD26eYSBBqC3NAhzNAhw_540_540.jpg",
  name: "解烁星",
  department: "河南一一科技有限公司",
  sort: "用户端",
});

onBeforeMount(() => {
  userInfo.value = useLoginStore().loginData.user_info;
  userInfo.value.sort = "用户端";
});
// 路由跳转
const router = useRouter();

// 点击相关页面
const goBtnPage = (name) => {
  if (name == "service") {
    proxy.$_dd.ready(() => {
      proxy.$_dd.biz.util
        .openLink({
          url: `https://page.dingtalk.com/wow/dingtalk/act/serviceconversation?wh_biz=tm&showmenu=false&goodsCode=${
            import.meta.env.VITE_APP_CORP_PRODUCT
          }&corpId=${useLoginStore().corpId}&token=${
            import.meta.env.VITE_APP_DT_GOODS_TOKEN
          }`,
          onSuccess: (rsult) => {},
          onFail: (err) => {},
        })
        .catch((err) => {});
    });
  } else if (name == "warning") {
    window.location.href =
      "https://alidocs.dingtalk.com/i/spaces/3YxXAaEdZKbBQXNy/overview?utm_medium=im_card&utm_source=im";
  } else {
    router.push({ name });
  }
  // console.log(name);
};

// 选项按钮
const btns = reactive([
  // {
  //   icon: "service-o",
  //   title: "产品咨询",
  //   goName: "service",
  // },
  {
    icon: "warning-o",
    title: "帮助手册",
    goName: "warning",
  },
  // {
  //   icon: "info-o",
  //   title: "关于",
  //   goName: "about",
  // },
]);

// 方式选择
const typeColumn = [
  { text: "用户端", value: 0 },
  { text: "管理端", value: 1 },
];
const showPicker = ref(false);
const flowTypeText = ref("");
const flowTypeValue = ref(0);

// 点击切换身份
const switchID = () => {
  showPicker.value = true;
};

// 点击选项
const switchFlowType = (item) => {
  // console.log(item);
  flowTypeValue.value = item.value;
  flowTypeText.value = item.text;
  userInfo.value.sort = flowTypeText.value;
  showPicker.value = false;
};
</script>
  
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 92px;
  padding: 16px 12px;
  background-color: #fff;
  box-sizing: border-box;

  .left {
    display: flex;
    .img {
      img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
    }

    .desc {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      margin-left: 12px;

      .l1 {
        .name {
          margin-right: 6px;
          font-size: 17px;
          line-height: 22px;
        }

        .identity {
          position: relative;
          top: -2px;
          padding: 2px 8px;
          line-height: 16px;
          font-size: 10px;
          background: #e7f0f9;
          color: #007fff;
          border-radius: 8px;
        }
      }

      .l2 {
        font-size: 12px;
        line-height: 18px;
        color: #9e9fa1;
      }
    }
  }

  .right {
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    border-radius: 13px;
    background-color: #007fff;
    color: #fff;
  }
}

.van-popup {
  .headers {
    margin-top: 14px;
    text-align: center;
    font-size: 17px;
    line-height: 25px;
  }

  .p {
    display: flex;
    justify-content: space-between;
    width: 86%;
    margin-left: 7%;

    img {
      width: 14px;
      height: 10px;
    }
  }
}

.btns {
  margin-top: 30px;

  .btn {
    display: flex;
    align-items: center;
    position: relative;
    margin-left: 5%;
    width: 90%;
    height: 56px;
    border-radius: 6px;
    background-color: #fff;
    .licon {
      margin-left: 18px;
    }
    span {
      margin-left: 16px;
      font-size: 17px;
    }

    .ricon {
      position: absolute;
      right: 16px;
      color: #a2a3a5;
    }
  }
}
</style>