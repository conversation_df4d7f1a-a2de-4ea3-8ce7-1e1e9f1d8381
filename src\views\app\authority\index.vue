<template>
  <div class="content">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="taskflowList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-if="taskflowList.length >= 1">
        <template v-for="(el, index) in taskflowList" :key="index">
          <div class="sort-item">
            <div class="left" @click="goDetail(el.id)">
              <div class="title">{{ el.title }}</div>
              <div class="line">
                <div class="name">管理员：{{ el.admin_names }}</div>
                <!-- <div class="name">
                  管理范围：{{ manageList[el.manage_scope].label }}
                </div> -->
                <div class="name">分配权限：{{ el.action_titles }}</div>
              </div>
            </div>
            <div class="icon" @click="showPopup(el)">
              <van-icon name="ellipsis" />
            </div>
          </div>
        </template>
      </template>
      <template v-else-if="showDesc && taskflowList.length == 0">
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </van-list>
  </div>
  <van-action-bar class="bar" v-if="taskflowList.length>0">
    <van-button class="add" type="primary" @click="goAdd">新增权限</van-button>
  </van-action-bar>
</template>
        
  <script setup>
import { ref, reactive, onBeforeMount, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 接口配置数据
let postData = ref({
  page: 0,
  per_page: 10,
});

// 数据列表
let taskflowList = ref([]);
// 列表总数
let total = ref(0);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无内容");
// 列表加载
const loading = ref(false);
const finished = ref(false);
// 管理范围
const manageList = reactive([
  {
    label: "全组织",
    value: 0,
  },
  {
    label: "所在部门和下级部门",
    value: 1,
  },
  {
    label: "所在部门",
    value: 2,
  },
]);

// onBeforeMount(() => {
//   // 获取任务分配列表
//   getList();
// });

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("permission_group/get_ls", postData.value)
    .then((res) => {
      res.result.data.forEach((el) => {
        taskflowList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;

      loading.value = false;

      // 加载完毕
      if (taskflowList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
      taskflowList.value=[]
      loading.value = false;
    });
};

// 列表加载
const onLoad = () => {
  // console.log("需要再次加载数据");
  postData.value.page = postData.value.page + 1;
  getList();
};

// 点击添加数据
const goAdd = () => {
  router.push({ name: "AuthorityAdd" });
};

// 点击跳转详情
const goDetail = (id) => {
  router.push({ name: "AuthorityInfo", query: { id } });
};

// 弹出层
const show = ref(false);
const operationTile = ref("");
const operationId = ref(0);
const actions = [
  { name: "修改", color: "#1678ff" },
  { name: "删除", color: "#1678ff" },
];
const showPopup = (el) => {
  proxy.$_dd.device.notification.actionSheet({
    title: el.title, //标题
    cancelButton: "取消", //取消按钮文本
    otherButtons: ["修改", "删除"],
    onSuccess: function (result) {
      console.log(result);
      if (result.buttonIndex == 0) {
        // 修改
        router.push({
          name: "AuthorityEdit",
          query: { id: el.id },
        });
      } else if (result.buttonIndex == 1) {
        delData(el.id);
      }
      //onSuccess将在点击button之后回调
      /*{
            buttonIndex: 0 //被点击按钮的索引值，Number，从0开始, 取消按钮为-1
        }*/
    },
    onFail: function (err) {},
  });
};

// 删除
const delData = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除此管理组么?",
  })
    .then(() => {
      proxy
        .$post("permission_group/post_del", {
          id,
        })
        .then((res) => {
          // console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("删除成功");
            postData.value.page = 0;
            taskflowList.value = [];
            onLoad();
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    })
    .catch(() => {
      // on cancel
    });
};
</script>
        
  <style lang="scss" scoped>
.header {
  display: flex;
  height: 44px;
  line-height: 44px;
  background-color: #fff;
  text-align: center;

  div {
    flex: 0 0 50%;
    font-size: 14px;
  }

  .active {
    position: relative;
    font-weight: 550;
  }

  .active::after {
    content: "";
    display: inline-block;
    position: absolute;
    left: 50%;
    bottom: 4px;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    border-radius: 1px;
    background-color: #000;
  }
}

.content {
  padding: 16px 0;

  .sort-item {
    display: flex;
    justify-content: space-between;
    margin-left: 5%;
    margin-top: 12px;
    padding: 16px;
    width: 90%;
    // height: 79px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .left {
      .title {
        margin-bottom: 16px;
        font-size: 17px;
      }

      .line {
        margin-bottom: 16px;
        font-size: 13px;
        color: #a2a3a5;
        line-height: 21px;

        .img {
          margin-right: 6px;
          width: 21px;
          height: 21px;
          border-radius: 3px;
          background-color: #007fff;
          color: #fff;
          text-align: center;

          img {
            width: 100%;
            height: 100%;
            border-radius: 3px;
          }
        }

        .name {
          position: relative;
          padding-right: 12px;
        }

        .time {
          margin-left: 12px;
        }
      }
      .type {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 6px;
        font-size: 10px;
        line-height: 17px;
        background-color: #e0efff;
        color: #007fff;
      }
    }
    .icon {
      // width: 30px;
      // background-color: pink;
      color: #c8c8c9;
    }
  }
}

.bar {
  height: 60px;

  .add {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>