<template>
 <div v-if="taskflowList.length>0">
   <div class="content">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :finished-text="taskflowList.length != 0 ? '没有更多了' : ''"
        @load="onLoad"
      >
        <template v-if="taskflowList.length >= 1">
          <template v-for="(el, index) in taskflowList" :key="index">
            <div class="sort-item">
              <div class="left" @click="goDetail(el.id)">
                <div class="title">
                  {{ el.title }}
                </div>
                <div class="line">人员：{{ el.usersNames }}</div>
              </div>
              <div class="icon" @click="showPopup(el)">
                <van-icon name="ellipsis" />
              </div>
            </div>
          </template>
        </template>
        <template v-else-if="showDesc && taskflowList.length == 0">
          <van-empty
            image="/icons/acquiesce.png"
            image-size="250"
            :description="description"
          />
        </template>
      </van-list>
    </div>
    <van-action-bar class="bar" v-if="taskflowList.length>0">
      <van-button class="add" type="primary" @click="goAdd">新增岗位</van-button>
    </van-action-bar>
 </div>
 <template v-else>
    <van-empty
      image="/icons/acquiesce.png"
      image-size="250"
      :description="description"
    />
  </template>
</template>
        
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

// 路由跳转
const route = useRoute();
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 接口配置数据
let postData = ref({
  page: 0,
  per_page: 10,
});
// 头部 tab 数字提示
let doneNum = ref(0);
// 数据列表
let taskflowList = ref([]);
// 列表总数
let total = ref(0);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无任务");
// 列表加载
const loading = ref(false);
const finished = ref(false);

onBeforeMount(() => {});

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("position/get_ls", postData.value)
    .then((res) => {
      if(res.errcode==510){
        taskflowList.value  = [];
        loading.value = false;
        description.value="权限不足"
        //showFailToast("权限不足");
        returns
      }
      console.log(res,9999);
      if (postData.value.is_all == 0) {
        doneNum.value = res.result.total;
      }
      res.result.data.forEach((el) => {
        const usersName = JSON.parse(el.userlst).users.map((item) => {
          return item.name;
        });
        el.usersNames = usersName.join("、");
        taskflowList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;
      loading.value = false;
      // 加载完毕
      if (taskflowList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
      taskflowList.value  = [];
      loading.value = false;
    });
};

// 列表加载
// const onLoad = () => {
// onMounted() => {
//   console.log("需要再次加载数据");
//   postData.value.page = postData.value.page + 1;
//   getList();
// };
onMounted(() => {
  postData.value.page = postData.value.page + 1;
  getList();
});

// 点击添加数据
const goAdd = () => {
  router.push({ name: "postAdd" });
};

const showPopup = (el) => {
  proxy.$_dd.device.notification.actionSheet({
    title: el.title, //标题
    cancelButton: "取消", //取消按钮文本
    otherButtons: ["修改", "删除"],
    onSuccess: function (result) {
      console.log(result);
      if (result.buttonIndex == 0) {
        // 修改
        router.push({
          name: "postEdit",
          query: { id: el.id },
        });
      } else if (result.buttonIndex == 1) {
        delData(el.id);
      }
      //onSuccess将在点击button之后回调
      /*{
              buttonIndex: 0 //被点击按钮的索引值，Number，从0开始, 取消按钮为-1
          }*/
    },
    onFail: function (err) {},
  });
};
// 删除
const delData = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除此岗位么?",
  })
    .then(() => {
      proxy
        .$post("position/post_del", { id })
        .then((res) => {
          // console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("删除成功");
            postData.value.page = 0;
            taskflowList.value = [];
            onLoad();
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    })
    .catch(() => {
      // on cancel
    });
};
</script>
        
  <style lang="scss" scoped>
.header {
  display: flex;
  height: 44px;
  line-height: 44px;
  background-color: #fff;
  text-align: center;

  div {
    flex: 0 0 50%;
    font-size: 14px;
  }

  .active {
    position: relative;
    font-weight: 550;
  }

  .active::after {
    content: "";
    display: inline-block;
    position: absolute;
    left: 50%;
    bottom: 4px;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    border-radius: 1px;
    background-color: #000;
  }
}

.content {
  padding-bottom: 75px;
  padding-top: 16px;

  .sort-item {
    display: flex;
    justify-content: space-between;
    margin-left: 5%;
    margin-top: 12px;
    padding: 16px;
    width: 90%;
    // height: 79px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .left {
      .title {
        display: flex;
        font-size: 17px;
        line-height: 22px;
        margin-bottom: 8px;

        img {
          width: 22px;
          height: 22px;
          margin-right: 10px;
        }
      }

      .line {
        font-size: 13px;
        display: flex;
        color: #a2a3a5;
        line-height: 21px;
      }
    }
    .icon {
      color: #c8c8c9;
    }
  }
}

.bar {
  height: 60px;

  .add {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>