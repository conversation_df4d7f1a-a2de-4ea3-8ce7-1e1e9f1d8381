<template>
  <div class="content">
    <div class="text">云一工作项目管理系统</div>
    <van-image class="icon" :src="avatar" />
    <div class="button" @click="confirm">确认登录</div>
    <van-overlay :show="show">
      <van-loading size="24px" vertical :style="{ marginTop: '40vh' }"
        >登录中...</van-loading
      >
    </van-overlay>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
  showToast,
  showSuccessToast,
  showFailToast,
  showLoadingToast,
  closeToast,
} from "vant";
// 接口
const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
const app = useLoginStore();
let { query } = route;
let show = ref(false);
let avatar = ref("https://file.qixuw.com/tool/logo.png");

onBeforeMount(() => {
  show.value = true;
  // showToast({
  //   message: "登录中...",
  //   forbidClick: true,
  //   // duration: 0,
  // });
  if (query.corpid) {
    app.corpId = query.corpid;
  }
  app
    .h5Login()
    .then((res) => {
      console.log("res>>", res);
      show.value = false;
      // showSuccessToast("登录成功");
      avatar.value = res.result.user_info.avatar;
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    });
});

const confirm = () => {
  proxy
    .$get("device/get_qrcode_login", {
      ...query,
    })
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        // showSuccessToast("确认成功");
        setTimeout(function () {
          proxy.$_dd.biz.navigation.close();
        }, 1000);
      } else {
        showFailToast(res.errmsg);
        setTimeout(function () {
          proxy.$_dd.biz.navigation.close();
        }, 1000);
      }
    })
    .catch((err) => {
      setTimeout(function () {
        proxy.$_dd.biz.navigation.close();
      }, 1000);
      console.log(err);
    });
};
</script>

<style lang="scss" scoped>
.content {
  height: 100vh;
  position: relative;
  background-image: url("https://file.qixuw.com/tool/49cb85cb2b71c469c5d21fa55d7f0e5ea3536d47bd51-P2jGX3_fw658 (1).png");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 100% 120%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;

  .text {
    margin-top: 44px;
    font-size: 17px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0px;
  }

  .icon {
    margin-top: 46px;
    width: 126px;
    height: 126px;
    border-radius: 16px;
    overflow: hidden;
  }

  .button {
    margin-top: 183px;
    width: 257px;
    height: 48px;
    border-radius: 10px;
    background: linear-gradient(102deg, #007fff 61%, #0062ff 95%);
    text-align: center;
    font-size: 18px;
    font-weight: normal;
    line-height: 48px;
    color: #fff;
  }
}
</style>