<template>
  <van-field
    v-model="fileList"
    :label="props.label"
    input-align="right"
    readonly
    @click="openSelect"
  >
    <template #left-icon v-if="props.is_second">
      <img class="second" src="/icons/second.png" alt="" />
    </template>
    <template #right-icon>
      <template v-if="props.readonly">
        <van-icon name="add-o" />
      </template>
      <template v-else-if="!props.readonly">
        <van-uploader :accept="props.uploadType" :after-read="afterRead">
          <van-icon name="add-o" />
        </van-uploader>
      </template>
    </template>
  </van-field>
  <template v-if="isShow">
    <div class="card" v-for="(el, i) in urls" :key="i">
      <div class="left">
        <div class="img">
          <!-- {{ el.url.match(/.{4}$/)[0] }} -->
          <template
            v-if="
              el.url.match(/.{4}$/)[0] == 'jpeg' ||
              el.url.match(/.{3}$/)[0] == 'MOV' ||
              el.url.match(/.{3}$/)[0] == 'jpg' ||
              el.url.match(/.{3}$/)[0] == 'png'
            "
          >
            <van-image
              width="48"
              height="48"
              radius="6"
              show-loading
              show-error
              fit="cover"
              :src="el.url"
            />
          </template>
          <template v-else>
            <van-image
              width="48"
              height="48"
              radius="6"
              show-loading
              show-error
              fit="cover"
              src="/icons/Excel.png"
            />
          </template>
        </div>
        <div class="desc">
          <div class="title">
            {{ el.name }}
          </div>
          <div class="info">
            <div class="content">{{ el.size }}M</div>
            <div
              class="preview"
              @click="preview(el.url)"
              v-if="props.isPreview"
            >
              预览
            </div>
          </div>
        </div>
      </div>
      <div class="right" @click="delUrl(el)">
        <img src="/icons/del.png" alt="" />
      </div>
    </div>
  </template>
  <!-- <van-overlay :show="show" @click="show = false">
    <div class="wrapper">
      <div class="box" @click.stop>
        <audio controls :src="url"></audio>
      </div>
    </div>
  </van-overlay> -->
</template>
      
<script setup>
import {
  ref,
  reactive,
  onBeforeUpdate,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
  watch,
} from "vue";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  showToast,
  showLoadingToast,
  closeToast,
} from "vant";
import { useRouter } from "vue-router";

// import "vue3-video-play/dist/style.css";

// import vue3videoPlay from "vue3-video-play";

// 接口
const { proxy } = getCurrentInstance();

// emit 发送数据
const emit = defineEmits(["postData"]);
// fileList
let urls = ref([]);
// 是否展示文件列表
let isShow = ref(false);

const props = defineProps({
  label: String,
  keyShow: String,
  is_second: {
    type: Boolean,
    default: false,
  },
  uploadType: {
    type: String,
    default: "",
  },
  file: {
    type: String,
    default: "",
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  multipleChoice: {
    type: Boolean,
    default: false,
  },
  isPreview: {
    type: Boolean,
    default: true,
  },
});

onMounted(() => {
  // console.log("prop.file", props.file);
  if (props.file) {
    isShow.value = true;
    urls.value = JSON.parse(props.file);
  }
});

watch(props, (val) => {
  if (props.file && !isShow.value) {
    isShow.value = true;
    urls.value = JSON.parse(props.file);
  }
});

// 上传
const afterRead = (fileObj) => {
  let pushFile = {};
  showLoadingToast({
    message: "上传中...",
    forbidClick: true,
    duration: 0,
  });
  // url.value = "";
  const requestHeader = import.meta.env.VITE_APP_FILE_UPLOAD;
  const blob = new Blob([fileObj.file]);
  const size = blob.size;
  // 转换成MB
  const sizeMB = size / 1024 / 1024;
  // 保留两位小数
  pushFile.size = sizeMB.toFixed(2);
  // console.log("上传文件", fileObj.file.name);
  pushFile.name = fileObj.file.name;
  proxy
    .$upload(`${requestHeader}file/post_upload`, {
      file: fileObj.file,
      corp_product:import.meta.env.VITE_APP_CORP_PRODUCT,
      upload_type:'local',
      name_type:'org',
      dir_name:'tool'
    })
    .then((res) => {
      closeToast();
      showSuccessToast("上传成功");
      console.log("上传文件结果", res.result);
      isShow.value = true;
      pushFile.url = res.result;
      urls.value.push(pushFile);
      emit("postData", urls.value);
    })
    .catch((err) => {
      console.log(err);
    });
};

// 删除视频
const delUrl = (el) => {
  urls.value = urls.value.filter((n) => n.url != el.url);
  emit("postData", urls.value);
};

let show = ref(false);
// 预览
const preview = (url) => {
  // 判断资源类型
  if (props.uploadType == ".mp3") {
    show.value = true;
  } else {
    showImagePreview([url]);
  }
};
</script>
      
    <style lang="scss" scoped>
// 二级头
.second {
  width: 24px;
  height: 24px;
}

.card {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    width: 90%;
    display: flex;

    .desc {
      width: 80%;
      margin-left: 12px;

      .title {
        width: 100%;
        font-size: 17px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .info {
        display: flex;
        margin-top: 6px;
        font-size: 14px;

        .content {
          margin-right: 34px;
          color: #a2a3a5;
        }

        .preview {
          color: #317ed0;
        }
      }
    }
  }

  .right {
    img {
      width: 14px;
      height: 14px;
    }
  }
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>