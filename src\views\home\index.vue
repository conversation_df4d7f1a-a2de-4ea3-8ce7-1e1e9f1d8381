<template>
  <div class="wrapper">
    <div @click="leave">home</div>
    <div>{{ name }}</div>
    <div @click="click">{{ age }}</div>
  </div>
</template>
<script setup>
import { ref } from "vue";
const router = useRouter();
const name = ref("home");
const age = ref(18);
const leave = () => {
  router.back();
};
const click = () => {
  router.push("test/subcom");
};
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: 100vh;
  background: red;
}
</style>
