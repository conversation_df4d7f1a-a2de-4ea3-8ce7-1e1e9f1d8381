import { defineStore } from "pinia";
// import { getCurrentInstance } from "vue";
// import { data } from "./test";
export const useLoginStore = defineStore("login", {
  state: () => {
    return {
      env: import.meta.env,
      corpId: "",
      loginData: {},
      homeCofig: {
        reportOrOrder: 0,
      },
      proxy: null,
    };
  },
  getters: {
    token: (state) => state.loginData.token,
  },
  actions: {
    setProxy(proxyInstance) {
      this.proxy = proxyInstance;
    },
    h5Login() {
      if (!this.proxy) {
        return Promise.reject("组件实例未初始化，请先调用setProxy方法设置组件实例");
      }
      
      return new Promise((resolve, reject) => {
        this.ddInit()
        .then((res) => {
            console.log("h5Login调用成功--------->");
            this.getAuthCode()
              .then((codeData) => {
                this.userLogin(codeData.code)
                  .then((res) => {
                    resolve(res);
                  })
                  .catch((err) => {
                    reject(err);
                  });
              })
              .catch((err) => {
                reject(err);
              });
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    ddInit() {
      let url = window.location.href;
      return new Promise((resolve, reject) => {
        this.proxy
          .$post(this.env.VITE_APP_USER_API + "sys/get_jsapi_ticket", {
            url: url,
            corp_product: this.env.VITE_APP_CORP_PRODUCT,
            corpid: this.corpId,
            corp_type: this.env.VITE_APP_CORP_TYPE,
            types: this.env.VITE_APP_TYPES,
          })
          .then((res) => {
            if (!res.errcode) {
              res = res.result;
              this.appid = res.agentid;
              this.proxy.$_dd.config({
                agentId: res.agentid, // 必填，微应用ID
                corpId: res.corpid, //必填，企业ID
                timeStamp: res.time, // 必填，生成签名的时间戳
                nonceStr: res.noncestr, // 必填，生成签名的随机串
                signature: res.sign, // 必填，签名
                jsApiList: [ 
                  "biz.contact.complexPicker",
                  "biz.contact.departmentsPicker",
                  "biz.calendar.chooseOneDay",
                  "biz.ding.create",
                  "biz.util.scanCard",
                  "biz.util.scan",
                  "biz.util.timepicker",
                  "biz.util.openLink",
                  "biz.contact.departmentsPicker",
                  "biz.contact.rolesPicker",
                  "biz.map.search",
                ],
              });
              this.proxy.$_dd.ready(() => {
                this.proxy.$_dd.error((err) => {
                  reject(err);
                });
                resolve(res);
              });
            } else {
              reject(res.errmsg);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    getAuthCode() {
      return new Promise((resolve, reject) => {
        this.proxy.$_dd.runtime.permission.requestAuthCode({
          corpId: this.corpId,
          onSuccess: function (res) {
            resolve(res);
          },
          fail: function (err) {
            reject(err);
          },
        });
      });
    },
    userLogin(AuthCode, corp_type) {
      return new Promise((resolve, reject) => {
        this.proxy
          .$post("sys/get_login", {
            // .$get("sys/get_login", {
            code: AuthCode,
            corp_type: corp_type || this.env.VITE_APP_CORP_TYPE,
            types: this.env.VITE_APP_TYPES,
            corpid: this.corpId,
            corp_product: this.env.VITE_APP_CORP_PRODUCT,
          })
          .then((res) => {
            if (!res.errcode) {
              this.loginData = res.result;
              resolve(res);
            } else {
              throw res.errmsg;
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    wwInit() {
      let that = this;

      async function getConfigSignature(url) {
        let res = await that.proxy.$post(
          that.env.VITE_APP_USER_API + "sys/get_jsapi_ticket",
          {
            url: url,
            corp_product: that.env.VITE_APP_CORP_PRODUCT,
            corpid: that.corpId,
            corp_type: "qyweixin",
            types: that.env.VITE_APP_TYPES,
          }
        );
        console.log(8888888888, res);

        // 根据 url 生成企业签名
        // 生成方法参考 https://developer.work.weixin.qq.com/document/path/90539
        let {
          time: timestamp,
          noncestr: nonceStr,
          sign: signature,
        } = res.result;
        return { timestamp, nonceStr, signature };
      }
      async function getAgentConfigSignature(url) {
        let res = await that.proxy.$post(
          that.env.VITE_APP_USER_API + "sys/get_jsapi_ticket",
          {
            url: url,
            corp_product: that.env.VITE_APP_CORP_PRODUCT,
            corpid: that.corpId,
            corp_type: "qyweixin",
            types: that.env.VITE_APP_TYPES,
          }
        );
        // 根据 url 生成企业签名
        // 生成方法参考 https://developer.work.weixin.qq.com/document/path/90539
        let {
          time: timestamp,
          noncestr: nonceStr,
          agent_sign: signature,
        } = res.result;
        return { timestamp, nonceStr, signature };
      }

      return new Promise((resolve, reject) => {
        that.proxy
          .$post(that.env.VITE_APP_USER_API + "sys/get_auth_info", {
            corp_product: that.env.VITE_APP_CORP_PRODUCT,
            corpid: that.corpId,
            corp_type: "qyweixin",
            types: that.env.VITE_APP_TYPES,
          })
          .then((res) => {
            console.log("授权信息------->", res.agentid, res);
            res = res.result;
            this.proxy.$_dd.register({
              agentId: res.agentid,
              corpId: this.corpId, // 必填，当前用户企业所属企业ID
              jsApiList: [
                "getExternalContact",
                "selectExternalContact",
                "selectEnterpriseContact",
                "openUserProfile",
                "showMenuItems",
              ], // 必填，需要使用的JSAPI列表
              getConfigSignature, // 必填，根据url生成企业签名的回调函数
              getAgentConfigSignature,
              onConfigSuccess: (e) => {
                resolve(e);
              },
              onConfigFailed: (err) => {
                reject(err);
              },
              onAgentConfigFail: (err) => {
                reject(err);
              },
              onAgentConfigSuccess: (e) => {
                resolve(e);
              },
            });
          });
      });
    },
    getWxAuthCode() {
      console.log("window.location------------->", window);
      let { origin, pathname, hash, search } = window.location;

      if (search.includes("corpid=")) {
        let corpId = search.split("=")[1].split("&")[0];
        if (corpId) {
          this.corpId = corpId || window.localStorage.getItem("corpId");
          window.localStorage.setItem("corpId", this.corpId);
        }
      }
      if (search.includes("code=")) {
        return new Promise((resolve, reject) => {
          let AuthCode = search.split("=")[1].split("&")[0];
          this.userLogin(AuthCode, "qyweixin").then((res) => {
            this.wwInit()
              .then((res) => {
                this.proxy.$_dd.initOpenData({
                  success: (res) => {
                    console.log("initOpenData调用成功------》", res);
                  },
                  fail: (err) => {
                    console.log("initOpenData调用失败------》", err);
                  },
                });
                resolve(res);
              })
              .catch((err) => {
                reject(err);
              });
          });
        }).catch((err) => {
          console.log(err);
        });
      } else {
        let REDIRECT_URI = origin + pathname + hash;
        let url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.corpId
          }&redirect_uri=${encodeURIComponent(
            REDIRECT_URI
          )}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`;
        console.log("调用url----------->", REDIRECT_URI, url);
        console.log("调用url------1----->", this);
        console.log("调用url-----2------>", this.corpId);
        console.log("调用url-----3------>", window.localStorage);
        window.location.href = url;
      }
    },
  },

  // persist: true,
  // persist: {
  //   enabled: true,
  //   strategies: [
  //     {
  //       key: "homeCofig",
  //       // storage: window.sessionStorage,
  //       paths: ["homeCofig.reportOrOrder"],
  //     },
  //     {
  //       key: "corpId",
  //       // storage: window.sessionStorage,
  //       paths: ["corpId"],
  //     },
  //   ],
  //   beforeRestore: (ctx) => {
  //     console.log(`about to restore '${ctx.store.$id}'`)
  //   },
  //   afterRestore: (ctx) => {
  //     console.log(`just restored '${ctx.store.$id}'`)
  //   },
  // },
  persist: {
    paths: ["homeCofig.reportOrOrder", "corpId"],
    // enabled: true,
    // strategies: [
    //   {
    //     key: "homeCofig",
    //     // storage: window.sessionStorage,
    //     paths: ["homeCofig.reportOrOrder"],
    //   },
    //   {
    //     key: "corpId",
    //     // storage: window.sessionStorage,
    //     paths: ["corpId"],
    //   },   
    // ],
    beforeRestore: (ctx) => {
      console.log(`about to restore '${ctx.store.$id}'`);
    },
    afterRestore: (ctx) => {
      console.log(`just restored '${ctx.store.$id}'`);
    },
  },
});
