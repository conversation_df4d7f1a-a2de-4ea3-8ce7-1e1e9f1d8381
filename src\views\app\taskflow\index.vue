<template>
  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    cancel-text="取消"
    :description="operationTile"
    close-on-click-action
    @select="itemOption"
  />
  <div class="content">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="taskflowList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-if="taskflowList.length >= 1">
        <template v-for="(el, index) in taskflowList" :key="index">
          <div class="sort-item">
            <div class="left" @click="goDetail(el.id)">
              <div class="title">{{ el.title }}</div>
              <!-- <div class="type">运行方式: {{ el.type == 0 ? "串行" : "并行" }}</div> -->
            </div>
            <div class="icon" @click="showPopup(el)">
              <van-icon name="ellipsis" />
            </div>
          </div>
        </template>
      </template>
      <template v-else-if="showDesc && taskflowList.length == 0">
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </van-list>
  </div>
  <van-action-bar class="bar">
    <van-button class="add" type="primary" @click="goAdd"
      >新增任务流</van-button
    >
  </van-action-bar>
</template>
    
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 列表加载
const loading = ref(false);
const finished = ref(false);

// 分类列表
let taskflowList = ref([]);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无内容");
// 接口配置数据
let postData = ref({
  page: 0,
  per_page: 10,
});
// 列表总数
let total = ref(0);

// onBeforeMount(() => {});

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("flow/get_ls", postData.value)
    .then((res) => {
      console.log(res.result.data);
      res.result.data.forEach((el) => {
        taskflowList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;

      loading.value = false;
      // 加载完毕
      if (taskflowList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 列表加载
const onLoad = () => {
  console.log("需要再次加载数据");
  postData.value.page = postData.value.page + 1;
  getList();
};

// 点击添加数据
const goAdd = () => {
  router.push({ name: "TaskFlowAdd" });
};

// 点击跳转详情
const goDetail = (id) => {
  router.push({ name: "TaskFlowInfo", query: { id: id } });
};

// 弹出层
const show = ref(false);
const operationTile = ref("");
const operationId = ref(0);
const showPopup = (el) => {
  show.value = true;
  operationTile.value = el.title;
  operationId.value = el.id;
};
const actions = [
  { name: "修改", color: "#1678ff" },
  { name: "删除", color: "#1678ff" },
];

// 弹出层点击选项
const itemOption = (value) => {
  if (value.name == "修改") {
    modifyData();
  } else if (value.name == "删除") {
    delData();
  }
};

// 修改
const modifyData = () => {
  router.push({ name: "TaskFlowEdit", query: { id: operationId.value } });
};

// 删除
const delData = () => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除此任务流么?",
  })
    .then(() => {
      proxy
        .$post("flow/post_del", {
          id: operationId.value,
        })
        .then((res) => {
          console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("删除成功");
            postData.value.page = 0;
            taskflowList.value = [];
            onLoad();
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    })
    .catch(() => {
      // on cancel
    });
};

// // 分类列表
// const taskflowList = reactive([
//   {
//     title: "软件事业部任务流",
//     type: 0,
//     id: 0,
//   },
//   {
//     title: "钉钉事业部任务流",
//     type: 1,
//     id: 1,
//   },
// ]);
</script>
    
<style lang="scss" scoped>
.content {
  padding: 16px 0;

  .sort-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5%;
    margin-top: 12px;
    padding: 16px;
    width: 90%;
    height: 60px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .type {
        font-size: 14px;
        color: #747677;
      }
    }
    .icon {
      color: #c8c8c9;
    }
  }
}

.bar {
  height: 60px;

  .add {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>