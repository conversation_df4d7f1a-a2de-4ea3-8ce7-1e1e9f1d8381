import {
  createRouter,
  createWeb<PERSON>istory,
  createWebHashHistory,
} from "vue-router";
import routes from "@/config/router";
import { start, close } from "@/untils/nprogress";
import { setTransitionName, checkRouter } from "@/untils/app";

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
router.beforeEach((to, from) => {
  start();
  // if(to.path===from.path){return false}
  if (checkRouter(to, from)) {
    setTransitionName(to, from);
  } else {
    return { path: "/login", query: to.query };
  }
});
router.afterEach(() => {
  // checkBack(to, from)
  close();
});

export default router;
