<template>
    <div class="wrapper">
      <div @click="leave">subcom</div>
      <van-button type="primary">主要按钮</van-button>
        <van-button type="success">成功按钮</van-button>
        <van-button type="default">默认按钮</van-button>
        <van-button type="warning">警告按钮</van-button>
        <van-button type="danger">危险按钮</van-button>
        <van-cell-group>
        <van-cell title="单元格" value="内容" />
        <van-cell title="单元格" value="内容" label="描述信息" />
        </van-cell-group>
    </div>
  </template>
  <script setup>
  import { ref } from "vue";
  const router = useRouter();
  const name = ref("home");
  const age = ref(18);
  const leave = () => {
    router.back();
  };
  const click = () => {
    router.push("/");
  };
  </script>
  <style lang="scss" scoped>
  .wrapper {
    width: 100%;
    background: #fff;
  }
  </style>
  