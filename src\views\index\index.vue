<template>
  <van-popup v-model:show="show" position="bottom">
    <van-date-picker
      v-model="currentDate"
      :title="datePickerTitle"
      @cancel="closeDate"
      @confirm="changeDate"
    />
  </van-popup>
  <div class="content">
    <div class="statistics content-block">
      <div class="date">{{ today }} {{ week }}</div>
      <van-row class="task">
        <van-col span="8">
          <div class="teask-items line-task-items" @click="goTaskList(0)">
            <div class="number">{{ taskOverViewData.need_confirm }}</div>
            <div class="text">待验收</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="teask-items line-task-items" @click="goTaskList(1)">
            <div class="number">{{ taskOverViewData.need_finish }}</div>
            <div class="text">待完成</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="teask-items" @click="goTaskList(2)">
            <div class="number">{{ taskOverViewData.done }}</div>
            <div class="text">已完成</div>
          </div>
        </van-col>
      </van-row>
    </div>
    <div
      class="timeoutTask task-list content-block"
      v-if="timeoutTask.length >= 1"
    >
      <div class="label">逾期任务</div>
      <div class="taskList">
        <template v-for="(item, index) in timeoutTask" :key="index">
          <div class="taskItem">
            <div class="title" @click="goDetail(item)">
              <span class="left">
                <!-- <img
                  v-if="!item.immediately"
                  :src="`/icons/lv${item.priority}.png`"
                  alt=""
                />
                <img v-else :src="`/icons/fire.png`" alt="" /> -->
                {{ item.task_title }}
              </span>
              <span :style="{ fontSize: '13px', color: '#a2a3a5' }">{{
                item.status == 0 ? "待完成" : "待验收"
              }}</span>
            </div>
            <div class="desc">
              <van-row>
                <van-col span="8" class="verticalline">
                  <div class="user">
                    <template v-if="item.avatar">
                      <img :src="item.avatar" alt="" />
                    </template>
                    <template v-else>
                      <div class="img">
                        {{ item.user_name[0] }}
                      </div>
                    </template>
                    <span>{{ item.user_name }}</span>
                  </div>
                </van-col>
                <van-col span="8" class="verticalline">
                  <div class="start">
                    {{ item.begin_time }}
                  </div>
                </van-col>
                <van-col span="8">
                  <div class="end">
                    {{ item.end_time }}
                  </div>
                </van-col>
              </van-row>
            </div>
          </div>
          <div v-if="index + 1 != timeoutTask.length" class="line"></div>
        </template>
      </div>
    </div>
    <div class="elseTask task-list content-block">
      <template v-if="elseTask.length >= 1">
        <div class="taskList">
          <template v-for="(item, index) in elseTask" :key="index">
            <div class="taskItem">
              <div class="title" @click="goDetail(item)">
                <span class="left">
                  <!-- <img
                    v-if="!item.immediately"
                    :src="`/icons/lv${item.priority}.png`"
                    alt=""
                  />
                  <img v-else :src="`/icons/fire.png`" alt="" /> -->
                  {{ item.task_title }}
                </span>
                <span :style="{ fontSize: '13px', color: '#a2a3a5' }">{{
                  item.begin_time ? "待完成" : "未报时"
                }}</span>
              </div>
              <div class="desc">
                <van-row>
                  <van-col span="8" class="verticalline">
                    <div class="user">
                      <template v-if="item.avatar">
                        <img :src="item.avatar" alt="" />
                      </template>
                      <template v-else>
                        <div class="img">
                          {{ item.user_name[0] }}
                        </div>
                      </template>
                      <span>{{ item.user_name }}</span>
                    </div>
                  </van-col>
                  <van-col span="8" class="verticalline">
                    <div class="start" v-if="item.begin_time">
                      {{ item.begin_time }}
                    </div>
                    <div class="select" @click="showPopup(item)" v-else>
                      请选择时间
                    </div>
                  </van-col>
                  <van-col span="8">
                    <div class="end" v-if="item.end_time">
                      {{ item.end_time }}
                    </div>
                    <div class="select" @click="showPopup(item)" v-else>
                      请选择时间
                    </div>
                  </van-col>
                </van-row>
              </div>
            </div>
            <div v-if="index + 1 != elseTask.length" class="line"></div>
          </template>
        </div>
      </template>
    </div>
    <template
      v-if="timeoutTask.length == 0 && elseTask.length == 0 && showDesc"
    >
      <van-empty
        image="/icons/acquiesce.png"
        image-size="250"
        :description="description"
      />
    </template>
  </div>
</template>
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 日期星期
const today = ref("");
const week = ref("");

// 日期选择器
const show = ref(false);
const datePickerTitle = ref("选择日期");
// 时间选择器默认展示日期
// const currentDate = ref(["2021", "01", "01"]);
let currentDate = ref([]);

// 任务总览数据
let taskOverViewData = ref({});
// 个人逾期项目列表
let timeoutTask = ref([]);
// 个人未逾期项目列表
let elseTask = ref([]);
// 是否展示空状态图片
let showDesc = ref(false);
// 空状态文字
let description = ref("暂无任务");
// 储存需要更改任务id
let changedTaskId = ref({
  id: 0,
  string: "",
});

onBeforeMount(() => {
  // 获取个人任务总览
  proxy
    .$get("task/get_overview")
    .then((res) => {
      taskOverViewData.value = res.result;
      // console.log(taskOverViewData.value);
      showDesc.value = true;
    })
    .catch((err) => {
      console.log(err);
    });
  // 获取个人逾期项目列表
  getTimeoutTaskList();
  // 获得个人未预期项目列表
  getElseTaskList();
});

onMounted(() => {
  // 获取当天日期
  const date = new Date();
  const weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  today.value = `${date.getMonth() + 1}月${date.getDate()}日`;
  week.value = weekday[date.getDay()];
  const year = date.getFullYear(); //年份
  const month = date.getMonth() + 1; //月份（0-11）
  const day = date.getDate(); //天数（1到31）
  currentDate.value.push(year);
  currentDate.value.push(month);
  currentDate.value.push(day);
});

// 获取个人逾期项目列表
const getTimeoutTaskList = () => {
  proxy
    .$get("task/get_my_all", {
      delay: 1,
    })
    .then((res) => {
      // console.log(res);
      timeoutTask.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};
// 获得个人未预期项目列表
const getElseTaskList = () => {
  proxy
    .$get("task/get_my_all", {
      delay: 0,
    })
    .then((res) => {
      // console.log(res);
      elseTask.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 打开日期选择器
const showPopup = (el) => {
  // show.value = true;
  // if (title == "begin_time") {
  //   datePickerTitle.value = "开始日期";
  // } else {
  //   datePickerTitle.value = "截止日期";
  // }
  // changedTaskId.value.id = id;
  // changedTaskId.value.string = title;
  // console.log(changedTaskId.value);

  // console.log("修改时间", el);
  proxy.$_dd.device.notification.prompt({
    message: "请填写该项目工时（天）",
    title: "工作时长",
    // defaultText: "请输入时长",
    buttonLabels: ["取消", "确定"],
    onSuccess: function (res) {
if(res.value%1!==0){
  showFailToast("请输入整数");
  return;
}
      // 调用成功时回调
      // console.log(res);
      if (res.buttonIndex == 1) {
        proxy
          .$post("task/post_modify_time", {
            id: el.id,
            task_id: el.task_id,
            duration: res.value,
          })
          .then((res) => {
            console.log(res);
            if (res.errcode == 0) {
              showSuccessToast("提交成功");
              getElseTaskList();
            } else {
              showFailToast(res.errmsg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    onFail: function (err) {
      // 调用失败时回调
      // console.log(err);
    },
  });
};

// // 关闭日期选择器
// const closeDate = () => {
//   show.value = false;
//   getElseTaskList();
// };

// // 修改日期选择器
// const changeDate = (value) => {
//   showConfirmDialog({
//     title: "请确认选择日期",
//     message: `${datePickerTitle.value}为${value.selectedValues.join(
//       "-"
//     )}，注意：日期一旦确认无法更改`,
//   })
//     .then(() => {
//       console.log(value.selectedValues);
//       const postData = {
//         id: changedTaskId.value.id,
//       };
//       if (changedTaskId.value.string == "begin_time") {
//         postData.begin_time = value.selectedValues.join("-");
//       } else {
//         postData.end_time = value.selectedValues.join("-");
//       }
//       proxy
//         .$post("task/post_modify_time", postData)
//         .then((res) => {
//           console.log(res);
//           if (res.errcode == 0) {
//             showSuccessToast("提交成功");
//             getElseTaskList();
//           } else {
//             showFailToast(res.errmsg);
//           }
//         })
//         .catch((err) => {
//           console.log(err);
//         });
//       closeDate();
//     })
//     .catch(() => {
//       // on cancel
//     });
// };

// 点击跳转详情页
const goDetail = (item) => {
  console.log(item);
  router.push({
    name: "taskDetail",
    query: {
      id: item.task_id,
      node_id: item.id,
      is_operate: 0,
    },
  });
};

// 跳转到任务列表
const goTaskList = (status) => {
  // console.log(status);
  router.push({ name: "taskList", query: { status } });
};
</script>
<style lang="scss" scoped>
.content {
  padding-bottom: 100px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.content-block {
  margin-left: 3%;
  margin-top: 12px;
  width: 94%;
  background-color: #fff;
  border-radius: 8px;
}

// 统计数据头部
.statistics {
  height: 132px;
  font-weight: 550;

  .date {
    font-size: 17px;
    line-height: 50px;
    margin-left: 16px;

    .arr {
      margin-left: 6px;
      width: 12px;
      height: 12px;
      background: rgba(150, 150, 150, 0.24);
      color: #fff;
      border-radius: 50%;
      font-size: 12px;
      text-align: center;
      line-height: 12px;
    }
  }

  .task {
    margin-top: 6px;
    display: flex;
    justify-content: space-around;
    text-align: center;
    line-height: 22px;

    .teask-items {
      position: relative;
    }

    .line-task-items::after {
      display: block;
      content: "";
      position: absolute;
      top: 13px;
      right: 0;
      width: 1px;
      height: 26px;
      background-color: #eaebed;
    }

    .text {
      margin-top: 8px;
      font-size: 13px;
      font-weight: 550;
      // line-height: 22px;
      color: rgba(23, 26, 29, 0.6);
    }
  }
}

// 逾期任务
.task-list {
  overflow: hidden;
  .label {
    margin-top: 16px;
    margin-left: 12px;
    padding: 0 6px;
    width: 68px;
    height: 22px;
    display: inline-block;
    background: linear-gradient(109deg, #ff713d 59%, #ff5b1f 95%);
    color: #fff;
    border-radius: 8px;
    line-height: 22px;
  }
  .taskList {
    margin: 0 12px 16px;
    .taskItem {
      margin-top: 16px;

      .title {
        display: flex;
        justify-content: space-between;
        // align-content: center;
        margin: 16px 0;
        font-size: 17px;
        line-height: 22px;

        .left {
          display: flex;
        }

        img {
          width: 22px;
          height: 22px;
          margin-right: 10px;
        }
      }

      .desc {
        height: 38px;
        line-height: 38px;
        font-size: 13px;
        text-align: center;

        .verticalline {
          position: relative;
        }

        .verticalline::after {
          display: block;
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          width: 1px;
          height: 100%;
          background-color: #eaebed;
        }
        .user {
          display: flex;
          align-items: center;
          // justify-content: center;
          img {
            display: block;
            width: 28px;
            height: 28px;
            border-radius: 6px;
          }
          .img {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            background-color: rgb(0, 137, 255);
            color: #fff;
            text-align: center;
            line-height: 28px;
          }
          span {
            margin-left: 8px;
          }
        }

        .select {
          color: #a2a3a5;
        }
      }
    }

    .line {
      margin-top: 17px;
      height: 1px;
      width: 100%;
      background-color: rgba(126, 134, 142, 0.16);
    }
  }
}

.timeoutTask {
  .end {
    color: #ff5219;
  }
}
</style>
