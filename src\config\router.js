export const config = {
  //开启白名单验证
  // isOffWhiteList:
    // import.meta.env.VITE_APP_ISOFFWHITELIST === "false" ? false : true,
  //路由免验白名单
  whiteList: ["/login", '/index/detail', '/pcLogin']
  //   whiteList: [
  //     "/login",
  //     "/",
  //     '/app',
  //     '/mine',
  //     '/index/detail',
  //     '/index/list',
  //     '/app/taskallocation',
  //     '/app/taskallocation/add',
  //     '/app/taskgroup',
  //     '/app/taskgroup/add',
  //     '/app/taskgroup/edit',
  //     '/app/taskgroup/detail',
  //     '/app/taskflow',
  //     '/app/taskflow/add',
  //     '/app/taskflow/detail',
  //     '/app/zentao',
  //     '/app/authority',
  //     '/app/authority/info',
  //     '/app/authority/add',
  //     '/app/authority/edit',
  //     '/app/worklist',
  //     '/app/statuskanban',
  //     '/app/ganttview',
  //     '/app/taskstatistics',
  //     '/mine/about']
}
const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    name: "login",
    path: "/login",
    component: () => import(/* webpackChunkName: "login" */ "@/views/login"),
    children: [],
  },
  {
    name: "index",
    path: "/index",
    // redirect: '/login' ,
    component: () => import(/* webpackChunkName: "index" */ "@/views/index"),
  },
  {
    name: "taskDetail",
    path: "/index/detail",
    component: () => import("@/views/index/detail"),
  },
  {
    name: "taskList",
    path: "/index/list",
    component: () => import("@/views/index/list"),
  },
  {
    name: "app",
    path: "/app",
    component: () => import(/* webpackChunkName: "home" */ "@/views/app"),
    children: [],
  },
  {
    name: "TaskAllocation",
    path: "/app/taskallocation",
    component: () => import("@/views/app/taskallocation"),
  },
  {
    name: "TaskAllocationAdd",
    path: "/app/taskallocation/add",


    component: () => import("@/views/app/taskallocation/add"),
  },
  {
    name: "TaskAllocationEdit",
    path: "/app/taskallocation/edit",
    component: () => import("@/views/app/taskallocation/edit"),
  },
  {
    name: "TaskAllocationInfo",
    path: "/app/taskallocation/info",
    component: () => import("@/views/app/taskallocation/info"),
  },
  {
    name: "TaskGroup",
    path: "/app/taskgroup",
    component: () => import("@/views/app/taskgroup"),
  },
  {
    name: "TaskGroupAdd",
    path: "/app/taskgroup/add",
    component: () => import("@/views/app/taskgroup/add"),
  },
  {
    name: "TaskGroupEdit",
    path: "/app/taskgroup/edit",
    component: () => import("@/views/app/taskgroup/edit"),
  },
  {
    name: "TaskGroupDetail",
    path: "/app/taskgroup/detail",
    component: () => import("@/views/app/taskgroup/detail"),
  },
  {
    name: "TaskFlow",
    path: "/app/taskflow",
    component: () => import("@/views/app/taskflow"),
  },
  {
    name: "TaskFlowAdd",
    path: "/app/taskflow/add",
    component: () => import("@/views/app/taskflow/add"),
  },
  {
    name: "TaskFlowInfo",
    path: "/app/taskflow/info",
    component: () => import("@/views/app/taskflow/info"),
  },
  {
    name: "TaskFlowEdit",
    path: "/app/taskflow/edit",
    component: () => import("@/views/app/taskflow/edit"),
  },
  {
    name: "ZenTao",
    path: "/app/zentao",
    component: () => import("@/views/app/zentao"),
  },
  {
    name: "Authority",
    path: "/app/authority",
    component: () => import("@/views/app/authority"),
  },
  {
    name: "AuthorityAdd",
    path: "/app/authority/add",
    component: () => import("@/views/app/authority/add"),
  },
  {
    name: "AuthorityEdit",
    path: "/app/authority/edit",
    component: () => import("@/views/app/authority/edit"),
  },
  {
    name: "AuthorityInfo",
    path: "/app/authority/info",
    component: () => import("@/views/app/authority/info"),
  },
  {
    name: "WorkList",
    path: "/app/worklist",
    component: () => import("@/views/app/worklist"),
  },
  {
    name: "StatusKanban",
    path: "/app/statuskanban",
    component: () => import("@/views/app/statuskanban"),
  },
  {
    name: "GanttView",
    path: "/app/ganttview",
    component: () => import("@/views/app/ganttview"),
  },
  {
    name: "TaskStatistics",
    path: "/app/taskstatistics",
    component: () => import("@/views/app/taskstatistics"),
  },
  {
    name: "TaskStatisticsList",
    path: "/app/taskstatistics/list",
    component: () => import("@/views/app/taskstatistics/list"),
  },
  {
    name: "TaskStatisticsInfo",
    path: "/app/taskstatistics/info",
    component: () => import("@/views/app/taskstatistics/info"),
  },
  {
    name: "set",
    path: "/app/set",
    component: () => import("@/views/app/set"),
  },
  {
    name: "auditLog",
    path: "/app/auditLog",
    component: () => import("@/views/app/auditLog"),
  },
  {
    name: "post",
    path: "/app/post",
    component: () => import("@/views/app/post"),
  },
  {
    name: "postAdd",
    path: "/app/post/add",
    component: () => import("@/views/app/post/add"),
  },
  {
    name: "postEdit",
    path: "/app/post/edit",
    component: () => import("@/views/app/post/edit"),
  },
  // {
  //   name: "postInfo",
  //   path: "/app/post/info",
  //   component: () => import("@/views/app/post/info"),
  // },
  // 我的 模块
  {
    name: "mine",
    path: "/mine",
    component: () => import(/* webpackChunkName: "home" */ "@/views/mine"),
    children: [],
  },
  {
    name: "about",
    path: "/mine/about",
    component: () => import("@/views/mine/about"),
  },
  {
    name: "about",
    path: "/mine/about",
    component: () => import("@/views/mine/about"),
  },
  {
    name: "task",
    path: "/mine/task",
    component: () => import("@/views/mine/task"),
  },
  {
    name: "pcLogin",
    path: "/pcLogin",
    component: () => import("@/views/pcLogin"),
  },
  {
    name: "login",
    path: "/login",
    component: () => import(/* webpackChunkName: "login" */ "@/views/login"),
    children: [],
  }
]
export default routes;
