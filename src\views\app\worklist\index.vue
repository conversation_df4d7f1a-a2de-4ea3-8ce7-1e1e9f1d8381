<template>
  <!-- <van-popup
    v-model:show="showBottom"
    position="bottom"
    :style="{ height: '30%' }"
  >
    <van-date-picker
      v-model="currentDate"
      :title="datePickerTitle"
      @cancel="closeDate"
      @confirm="changeDate"
    />
  </van-popup> -->
  <van-row class="header">
    <van-col span="8" class="conheader" @click="selectPerson">
      <span class="heardimg"><img src="/icons/person.png" alt="" /><span v-if="selectDatalist.selectedCount==0">人员</span>
        <span>{{ selectDatalist.selectedCount?selectDatalist.departments.length?selectDatalist.departments[0].name:selectDatalist.users.length?selectDatalist.users[0].name:"":""}}</span>
        </span
      ><span><img class="point" src="/icons/point3.png" alt="" /></span>
    </van-col>
    <van-col span="8" class="conheader" @click="selectData('开始日期')">
      <span class="heardimg"
        ><img src="/icons/date.png" alt="" v-if="beginData == '开始日期'" />{{
          beginData
        }}</span
      >
      <span><img class="point" src="/icons/point3.png" alt="" /></span>
    </van-col>
    <van-col span="8" class="conheader" @click="selectData('结束日期')">
      <span class="heardimg"
        ><img src="/icons/date.png" alt="" v-if="beginData == '结束日期'" />{{
          endData
        }}</span
      >
      <span><img class="point" src="/icons/point3.png" alt="" /></span>
    </van-col>
  </van-row>
  <div class="content">
    <template v-if="workDirectory.length != 0">
      <van-collapse v-model="activeNames">
        <template v-for="(el, index) in workDirectory" :key="index">
          <van-cell-group inset class="workItem">
            <van-collapse-item :name="el.id" :is-link="false">
              <template #title>
                <img
                  class="rotate-arrow"
                  :class="activeNames.includes(el.id) ? 'active' : ''"
                  src="/icons/arrow.png"
                  alt=""
                />
                <span
                  class="collapse-title"
                  :style="{ background: el.color }"
                  >{{ el.name }}</span
                >
              </template>
              <template #value>
                <span>{{ el.num }}条记录</span>
              </template>
              <div class="taskList">
                <template v-for="(item, inx) in el.items" :key="inx">
                  <div class="taskItem" @click="goDetail(item.task_id)">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="desc">
                      <van-row>
                        <van-col span="8" class="verticalline">
                          <div class="user">
                            <template v-if="item.avater">
                              <img :src="item.avater" alt="" />
                            </template>
                            <template v-else>
                              <div class="img">
                                {{ item.user_name[0] }}
                              </div>
                            </template>
                            <span>{{ item.user_name }}</span>
                          </div>
                        </van-col>
                        <van-col span="8" class="verticalline">
                          <div
                            class="start"
                            :style="{ color: item.begin_time ? '' : '#a2a3a5' }"
                          >
                            {{
                              item.begin_time ? item.begin_time : "未选择日期"
                            }}
                          </div>
                        </van-col>
                        <van-col span="8">
                          <div
                            class="end"
                            :style="{
                              color: item.end_time
                                ? item.time_out
                                  ? '#ef405b'
                                  : ''
                                : '#a2a3a5',
                            }"
                          >
                            {{ item.end_time ? item.end_time : "未选择日期" }}
                          </div>
                        </van-col>
                      </van-row>
                    </div>
                  </div>
                  <div v-if="inx + 1 != el.items.length" class="line"></div>
                </template>
              </div>
            </van-collapse-item>
          </van-cell-group>
        </template>
      </van-collapse>
    </template>
    <template v-else>
      <van-empty
        image="/icons/acquiesce.png"
        image-size="250"
        :description="description"
      />
    </template>
  </div>
</template>
  
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import { useRouter } from "vue-router";

const router = useRouter();
// 接口
const { proxy } = getCurrentInstance();

const activeNames = ref([]);
// 任务列表
let workDirectory = ref();
// 背景颜色
const colors = reactive(["#ff713d", "#007fff", "#00ba46", "#45d9d2"]);
//
let description = ref("暂无内容");

// 日期选择器
const showBottom = ref(false);
const datePickerTitle = ref("选择日期");
let timeType = ref("");
// 时间选择器默认展示日期
const currentDate = ref(["2021", "01", "01"]);
const beginData = ref("开始日期");
const endData = ref("结束日期");
// let currentDate = ref([]);

// 发送数据
let postData = ref({
  // userid_list: "",
  // begin_time: "",
  // end_time: "",
});
let selectDatalist=ref({
  selectedCount:0
})
onBeforeMount(() => {
  // 获取任务列表
  getList();
  // 获取当天日期
  const date = new Date();
  const year = date.getFullYear(); //年份
  const month = date.getMonth() + 1; //月份（0-11）
  const day = date.getDate(); //天数（1到31）
  currentDate.value.push(year);
  currentDate.value.push(month);
  currentDate.value.push(day);
});

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("datav/get_work_list", postData.value)
    .then((res) => {
      // console.log(res.result);
      if (res.errcode == 0) {
        res.result.forEach((el, index) => {
          // console.log(el, index);
          el.color = colors[index % 3];
        });
        workDirectory.value = res.result;
      } else {
        // showFailToast(res.errmsg);
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 点击跳转详情页
const goDetail = (id) => {
  // console.log(id);
  router.push({ name: "TaskAllocationInfo", query: { id: id } });
};

// 打开日期选择器
const selectData = (type) => {
  // showBottom.value = true;
  // datePickerTitle.value = type;
  // timeType.value = type;
  proxy.$_dd.biz.calendar.chooseOneDay({
    default: new Date().getTime(),
    success: (res) => {
      const { timezone, chosen } = res; 
      const datej = new Date(chosen);
      const year = datej.getFullYear();
      let month = datej.getMonth() + 1;
      let day = datej.getDate();
      month = String(month).padStart(2, "0");
      day = String(day).padStart(2, "0");

      const formattedDate = `${year}-${month}-${day}`;
      console.log("Formatted Date:", formattedDate); // 输出：2021-07-03
      if (type == "开始日期") {
        beginData.value = formattedDate;
        postData.value.beginTime = formattedDate;
      } else {
        endData.value = formattedDate;
        postData.value.endTime = formattedDate;
      }
      getList()
    },
    fail: () => {},
    complete: () => {},
  });
};

// 关闭日期选择器
const closeDate = () => {
  showBottom.value = false;
};

// 修改日期选择器
const changeDate = (value) => {
  // console.log(currentDate.value);
  if (timeType.value == "开始日期") {
    postData.value.begin_time = "";
    postData.value.begin_time = value.selectedValues.join("-");
  } else {
    postData.value.end_time = "";
    postData.value.end_time = value.selectedValues.join("-");
  }
  closeDate();
  getList();
};

const selectPerson = () => {
  let directorLsts = ref();
  // let selectUsers = ref();
  // console.log(postData.value.userid_list);

  if (postData.value.userid_list) {
    directorLsts.value = JSON.parse(postData.value.userid_list);
  }

  console.log(directorLsts.value);
  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: false,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: directorLsts.value, //已选用户
    // pickedDepartments: selectDeparments.value, //已选部门
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      // 选择联系人或部门成功后的回调函数
      console.log(data,888);
      directorLsts.value = data.users.map((item) => {
        return item.emplId;
      });
      // console.log(directorLsts.value);
      selectDatalist.value=data;
      postData.value.userid_list = JSON.stringify(directorLsts.value);
      getList();
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
    },
  });
};

// const workDirectory = reactive([
//   {
//     name: "云一支付",
//     id: 1,
//     color: "#ff713d",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
//   {
//     name: "云一门岗",
//     id: 2,
//     color: "#007fff",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
//   {
//     name: "云一车管",
//     id: 3,
//     color: "#00ba46",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
//   {
//     name: "云一发布",
//     id: 4,
//     color: "#45d9d2",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
// ]);
</script>
  
  <style lang="scss" scoped>
.header {
  background: #fff;
  height: 55px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  .conheader {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-right: 1px solid #ededee;

    .point {
      width: 2px;
      height: 12px;
    }
  }
  .conheader:nth-child(3) {
    border: none;
  }
}
.heardimg {
  width: 6rem;
  img {
    width: 20%;
    vertical-align: bottom;
    margin-right: 0.6rem;
  }
}

// ::v-deep .van-cell {
//   flex-direction: row-reverse;
// }

// .collapse-title {
//   margin-left: 12px;
// }

.content {
  // padding: 16px 0;
  .workItem {
    margin-top: 16px;

    .rotate-arrow {
      position: relative;
      top: 4px;
      width: 18px;
      height: 18px;
      transition: all 0.2s;
      transform: rotate(-90deg);
    }

    .collapse-title {
      margin-left: 12px;
      padding: 0 6px;
      height: 26px;
      line-height: 26px;
      border-radius: 6px;
      color: #fff;
      font-size: 13px;
      display: inline-block;
    }

    .active {
      transform: rotate(0);
    }

    .taskList {
      color: #000;
      // margin: 0 12px 16px;
      .taskItem {
        // margin-top: 16px;

        .title {
          font-weight: 550;
          margin: 0 0 16px;
          font-size: 14px;
        }

        .desc {
          height: 38px;
          line-height: 38px;
          font-size: 13px;
          text-align: center;

          .user {
            display: flex;
            align-items: center;
            img {
              display: block;
              width: 28px;
              height: 28px;
              border-radius: 6px;
            }
            .img {
              width: 28px;
              height: 28px;
              border-radius: 6px;
              background-color: rgb(0, 137, 255);
              color: #fff;
              text-align: center;
              line-height: 28px;
            }
            span {
              margin-left: 8px;
            }
          }

          .verticalline {
            position: relative;
          }

          .verticalline::after {
            display: block;
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background-color: #eaebed;
          }
        }
      }

      .line {
        margin: 17px 0;
        height: 1px;
        width: 100%;
        background-color: rgba(126, 134, 142, 0.16);
      }
    }
  }
}
</style>