<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset class="item">
        <van-field
          v-model="form.title"
          label="权限组名称"
          placeholder="未设置"
          input-align="right"
          is-link
          :rules="[{ required: true, message: '请输入权限组名称' }]"
        />
        <van-field label="管理员" readonly @click="headbutton">
          <template #right-icon>
            <van-icon name="add-o" />
          </template>
        </van-field>
        <template v-if="directorLsts">
          <div class="imagestext">
            <template v-for="(el, index) in directorLsts" :key="index">
              <div class="imgItems" @click="delUser(index)">
                <img :src="el.avatar" alt="" />
                <div class="name">{{ el.name }}</div>
                <van-icon
                  class="del"
                  name="cross"
                  :style="{ color: '#bebec2', marginLeft: '13px' }"
                />
              </div>
            </template>
          </div>
        </template>
      </van-cell-group>
      <van-cell-group inset class="item">
        <!-- <van-field
          v-model="form.type_title"
          is-link
          readonly
          label="管理范围"
          placeholder="未设置"
          input-align="right"
          @click="showPickerFlow = true"
        /> -->
        <van-popup
          v-model:show="showPickerFlow"
          closeable
          close-icon-position="top-left"
          position="bottom"
          round
          :style="{ height: '50%' }"
          class="van-popup"
        >
          <div class="header">管理范围</div>
          <div class="options">
            <van-divider />
            <template v-for="(el, inx) in flowColumn" :key="inx">
              <div class="p" @click="switchFlowType(el)">
                <div>{{ el.label }}</div>
                <img
                  v-if="form.type == el.value"
                  src="/icons/select.png"
                  alt=""
                />
              </div>
              <van-divider />
            </template>
          </div>
        </van-popup>
        <van-field label="分配权限" readonly @click="showPopup">
          <template #right-icon>
            <van-icon name="add-o" />
          </template>
        </van-field>
        <!-- 分配权限弹出框 -->
        <van-popup
          class="power-popup"
          v-model:show="show"
          round
          closeable
          close-icon-position="top-left"
          position="bottom"
          :style="{ height: '80%' }"
        >
          <!-- 头部 -->
          <div class="header">
            <div class="center">分配权限</div>
            <div class="right-but" @click="submitPermisson">确定</div>
          </div>
          <div class="content">
            <!-- 全选 -->
            <div class="permisson-item all">
              <div class="left">全部</div>
              <div class="right">
                <van-switch
                  v-model="isAll"
                  @change="switchAll"
                  active-value="1"
                  inactive-value="0"
                />
              </div>
            </div>
            <!-- 基本权限 -->
            <div class="permisson-item">基本权限</div>
            <!-- 基本权限-内容 -->
            <template v-for="(el, index) in list" :key="index">
              <div class="permisson-item">
                <div class="left">
                  <van-checkbox
                    v-model="el.isSelect"
                    @change="switchFirst($event, index)"
                    >{{ el.title }}</van-checkbox
                  >
                </div>
                <div class="right" :class="el.isFold ? '' : 'active-right'">
                  <van-icon name="arrow-down" @click="switchFold(index)" />
                </div>
              </div>
              <!-- 子选择 -->
              <div class="permisson-item children" v-if="!el.isFold">
                <template v-for="(item, inx) in el.act" :key="inx">
                  <div class="childrenItem">
                    <van-checkbox
                      v-model="item.isSelect"
                      @change="switchTwo($event, index)"
                      >{{ item.title }}</van-checkbox
                    >
                  </div>
                </template>
              </div>
            </template>
          </div>
        </van-popup>
        <div class="permission-box" v-if="isShowPermisson">
          <template v-for="(el, index) in list" :key="index">
            <div
              class="permission-item"
              v-if="el.isSelect"
              @click="delPermisson(index)"
            >
              <div class="text">{{ el.title }}</div>
              <van-icon
                name="cross"
                :style="{ color: '#bebec2', marginLeft: '13px' }"
              />
            </div>
          </template>
        </div>
      </van-cell-group>
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit">
        提交
      </van-button>
    </van-action-bar>
  </van-form>
</template>
      
    <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { useRouter } from "vue-router";

// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 页面首次加载
onMounted(() => {
  getPermisson();
});

// 表格数据
let form = ref({
  title: "",
  type_title: "",
});

// 选择人组件
const directorLsts = ref();
const headbutton = () => {
  let selectUsers = ref();
  // console.log(directorLsts.value);

  if (directorLsts.value && directorLsts.value.length > 0) {
    selectUsers = directorLsts.value.map((item) => {
      return item.userid;
    });
  }

  // console.log(selectUsers);
  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: true,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: selectUsers, //已选用户
    // pickedDepartments: selectDeparments.value, //已选部门
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      // 选择联系人或部门成功后的回调函数
      console.log(data);
      directorLsts.value = data.users.map((item) => {
        return { avatar: item.avatar, name: item.name, userid: item.emplId };
      });
      //   directorLsts.value = data.users.map((item) => item.name).join(",");
      //   selectUsers.value = data.users.map((item) => item.emplId);
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
    },
  });
};

// 删除选人
const delUser = (index) => {
  directorLsts.value.splice(index, 1);
};

// 选择分组弹窗
const showPickerFlow = ref(false);
// 管理范围选择
let flowColumn = reactive([
  {
    label: "全组织",
    value: 0,
  },
  {
    label: "所在部门和下级部门",
    value: 1,
  },
  {
    label: "指定部门",
    value: 2,
  },
]);

// 点击选项
const switchFlowType = (item) => {
  form.value.type_title = item.label;
  form.value.manage_scope = item.value;
  showPickerFlow.value = false;
};

// 遮罩层
const show = ref(false);
// 权限数据
let list = ref();
// 展示所选权限
let isShowPermisson = ref(false);

const showPopup = () => {
  show.value = true;
  isShowPermisson.value = true;
};

const getPermisson = () => {
  list.value = [];
  proxy
    .$get("permission_action/get_all")
    .then(async (res) => {
      // 使用await确保数据完全加载
      list.value = await res.result;
      console.log(list.value);
      
      if (list.value && Array.isArray(list.value)) {
        list.value.forEach((el) => {
          el.isSelect = false;
          el.isFold = true;
          el.act = JSON.parse(el.acts);
          el.act.forEach((k) => {
            k.isSelect = false;
          });
        });
      } else {
        console.error("数据格式不正确:", res.result);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 全选开关
const isAll = ref(0);

// 全部选项改变
const switchAll = (value) => {
  //   console.log(value);
  if (value == 0) {
    // 取消全选
    list.value.forEach((el) => {
      el.isSelect = false;
      el.isFold = true;
      el.act.forEach((k) => {
        k.isSelect = false;
      });
    });
  } else {
    // 全选
    list.value.forEach((el) => {
      el.isSelect = true;
      el.isFold = false;
      el.act.forEach((k) => {
        k.isSelect = true;
      });
    });
  }
};

// 展开折叠
const switchFold = (index) => {
  list.value[index].isFold = !list.value[index].isFold;
  if (!list.value[index].isSelect) {
    list.value[index].isSelect = list.value[index].isSelect;
  }
};

// 第一级全选与取消全选
const switchFirst = (e, index) => {
  //   console.log(e, index);
  if (e) {
    // 一级全选
    list.value[index].isFold = false;
    list.value[index].act.forEach((k) => {
      k.isSelect = true;
    });
  } else {
    // 一级取消全选
    list.value[index].isFold = true;
    list.value[index].act.forEach((k) => {
      k.isSelect = false;
    });
    isAll.value = 0;
  }
};

// 第二级全选与取消全选
const switchTwo = (e, index) => {
  // console.log(e, index);
  if (e) {
    // 二级选择
    // list.value[index].isSelect = true;
  } else {
    // 二级取消选择
    console.log(list.value[index]);
    // list.value[index].isFold = true;
    let isCancel = ref(true);
    list.value[index].act.forEach((k) => {
      // k.isSelect = false;
      if (k.isSelect) {
        isCancel.value = false;
      }
    });
    if (isCancel.value) {
      list.value[index].isSelect = false;
    }
  }
};

// 分配权限确定
const submitPermisson = () => {
  show.value = false;
};

// 删除已选权限
const delPermisson = (index) => {
  list.value[index].isSelect = false;
};

// 提交任务
const submit = () => {
  // console.log(form.value);
  // console.log(directorLsts);
  // console.log(list.value);
  // 处理选人
  let postUserlst = ref({
    users: directorLsts.value ? directorLsts.value : [],
    departments: [],
  });
  form.value.userlst = JSON.stringify(postUserlst.value);
  // 处理权限选择
  let actions = ref([]);
  list.value.forEach((el) => {
    if (el.isSelect) {
      let acts = ref([]);
      el.act.forEach((k) => {
        if (k.isSelect) {
          acts.value.push({
            act: k.act,
            title: k.title,
          });
        }
      });
      actions.value.push({
        id: el.id,
        class: el.class,
        title: el.title,
        acts: acts.value,
      });
    }
  });
  form.value.actions = JSON.stringify(actions.value);
  // 打印
  console.log(form.value);
  proxy
    .$post("permission_group/post_add", form.value)
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("添加成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
      
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
}

.item {
  margin-top: 16px;
}
.step-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  width: 90%;
  margin-left: 5%;
  margin-top: 25px;
  margin-bottom: 12px;
}

.imagestext {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  margin-left: 4%;

  .imgItems {
    display: flex;
    background-color: #f2f2f6;
    line-height: 24px;
    padding: 6px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 14px;

    img {
      margin-right: 8px;
      width: 24px;
      height: 24px;
      border-radius: 3px;
    }
    .del {
      line-height: 24px;
    }
  }
}

.file-box {
  display: flex;
  position: relative;
  margin: 6px 0;
  margin-left: 5%;
  width: 90%;
  padding: 6px 8px;
  border-radius: 6px;
  background-color: #ebf5ff;
  box-sizing: border-box;
  text-align: center;

  img {
    width: 45px;
    height: 45px;
    border-radius: 4px;
  }

  .text {
    margin-left: 5%;
    width: 50%;
    line-height: 45px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .del-file {
    position: absolute;
    top: 50%;
    right: 3px;
    transform: translateY(-50%);
  }
}
// .options {
//   display: flex;
//   justify-content: space-between;
//   width: 90%;
// }

.van-popup {
  .header {
    margin-top: 14px;
    text-align: center;
    font-size: 17px;
    line-height: 25px;
  }

  .p {
    display: flex;
    justify-content: space-between;
    width: 86%;
    margin-left: 7%;

    img {
      width: 14px;
      height: 10px;
    }
  }
}

.add-step {
  margin-top: 16px;
}

.add-step ::v-deep .van-field__label {
  color: #007fff;
}

.add-step-icon {
  color: #007fff;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
.power-popup {
  overflow: hidden;
  .header {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-top: 10px;
    line-height: 30px;

    .center {
      width: 100%;
      text-align: center;
    }

    .right-but {
      position: absolute;
      top: 0;
      right: 16px;
      color: #1989fa;
    }
  }
  .content {
    height: 100%;
    padding: 0 16px;
    padding-bottom: 50px;
    font-size: 13px;
    box-sizing: border-box;
    overflow: auto;
  }
  .permisson-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #e9e9eb;

    .right {
      transform: rotate(0);
      transition: all 0.5s;
    }
    
    .active-right {
      transform: rotate(-180deg);
    }
  }

  .permisson-item:last-child {
    border: none;
  }

  .children {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: 20px;

    .childrenItem {
      padding: 8px 0;
      margin: 0;
      margin-right: 15px;
    }
  }
}

.permission-box {
  display: flex;
  flex-wrap: wrap;
  margin: 16px 0;
  margin-left: 4%;
  font-size: 14px;

  .permission-item {
    display: flex;
    padding: 6px;
    margin: 6px;
    border-radius: 4px;
    background-color: #f2f2f6;
  }
}
</style>