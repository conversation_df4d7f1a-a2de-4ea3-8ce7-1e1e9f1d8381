<template>
  <div class="content" ref="content">
    <div class="title">
      <span>{{ taskDetail.title }}</span>
      <span :style="{ fontSize: '12px', color: '#a2a3a5' }"
        >{{ taskDetail.node_info.user_name }}
        {{ taskDetail.node_info.title ? "-" : "" }}
        {{ taskDetail.node_info.title }}</span
      >
    </div>
    <div class="desc">{{ taskDetail.content }}</div>
    <div class="file">
      <template v-for="(el, x) in taskDetail.files" :key="x">
        <div class="fileItem">
          <img src="/icons/Excel.png" alt="" />
          <!-- <span>{{ el }}</span> -->
          <a :href="el.url">{{ el.name }}</a>
        </div>
      </template>
    </div>
    <div class="buts">
      <!-- <div class="user but">
        <van-icon name="contact" class="icon" />{{ taskDetail.users }}
      </div> -->
      <template v-if="taskDetail.parentnodes">
        <div class="user but">
          <van-icon name="contact" class="icon" />上个节点：{{
            taskDetail.parentnodes
          }}
        </div>
      </template>
      <template v-if="taskDetail.begin_time">
        <div class="start but">
          <van-icon name="notes-o" class="icon" />{{
            taskDetail.begin_time
              ? `开始日期：${taskDetail.begin_time}`
              : "开始日期"
          }}
        </div>
        <div class="end but">
          <van-icon name="notes-o" class="icon" />{{
            taskDetail.end_time
              ? `截止日期：${taskDetail.end_time}`
              : "截止日期"
          }}
        </div>
      </template>
      <template v-else>
        <div class="end but" :style="{ color: '#a2a3a5' }" @click="showPopup">
          <van-icon name="notes-o" class="icon" />请报时
        </div>
      </template>
      <div class="sort but">
        <van-icon name="label-o" class="icon" />任务进度:
        {{ taskDetail.node_info.progress }}%
      </div>
      <div class="sort but">
        <van-icon name="label-o" class="icon" />任务分组:
        {{ taskDetail.group_title }}
      </div>
      <template v-if="taskDetail.nextnodes">
        <div class="user but">
          <van-icon name="contact" class="icon" />下个节点：{{
            taskDetail.nextnodes
          }}
        </div>
      </template>
    </div>
    <div class="task-flow">
      <div class="title">任务流程</div>
      <van-divider />
      <template v-for="(item, index) in taskDetail.process" :key="index">
        <p>{{ item.showData }} {{ item.user_name }} {{ item.action }}</p>
      </template>
      <template v-if="taskDetail.type !== 0">
        <template v-if="taskDetail.status == 1">
          <template v-if="is_operate == 0">
            <div class="btn swhite">
              <span>等待验收</span>
            </div>
          </template>
          <template v-else>
            <div class="btns">
              <div class="bt sblue" @click="check(0)">
                <van-icon name="passed" class="icon" />
                <span>验收通过</span>
              </div>
              <div class="bt sred" @click="check(1)">
                <van-icon name="close" class="icon" />
                <span>未达标</span>
              </div>
            </div>
          </template>
        </template>
      </template>
      <template v-else-if="taskDetail.status == 0">
        <template v-if="taskDetail.begin_time">
          <div class="btn sblue" @click="finishTask">
            <van-icon name="edit" class="icon" />
            <span>每日提报</span>
          </div>
        </template>
        <template v-else>
          <div class="btn swhite">
            <van-icon name="passed" class="icon" />
            <span>未开始</span>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="btn swhite">
          <span>我已完成</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { useLoginStore } from "@/store/dingLogin";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 记录视口高度
const content = ref(null);
const app = useLoginStore();

// 详情数据信息
let taskDetail = ref({
  title: "",
  node_info: {},
});
let is_operate = ref(0);

onMounted(() => {
  // let url = window.location.href;
  let path = sessionStorage.getItem("login");
  console.log(path, "path");
  if (!path) {
    let { query } = route;
    console.log(query, "12312312312");
    if (query.corpid) {
      app.corpId = query.corpid;
    }

    app
      .h5Login()
      .then((res) => {
        console.log("登陆成功");
        const PostData = {
          id: route.query.id,
          node_id: route.query.node_id,
        };
        // 获取详情数据
        getTaskDetailData(PostData);
      })
      .catch((err) => {
        console.log(77777777, err);
        showToast(JSON.stringify(err));
      });


      const PostData = {
        id: route.query.id,
        node_id: route.query.node_id,
      };
      console.log(route.query.is_operate,3333);
      if (route.query.is_operate) {
        is_operate.value = route.query.is_operate;
      }
      // 获取详情数据
      getTaskDetailData(PostData);
  } else {
  }
});

// 获取详情数据
const getTaskDetailData = (postData) => {
  proxy
    .$get("task/get_my_info", postData)
    .then((res) => {
      for (let i in res.result) {
        let item = res.result[i];
        if (i == "flow") {
          res.result.users = `${item.title}，${item.finish}/${item.total}已完成`;
        } else if (i == "process") {
          // console.log(item);
          item.forEach((el) => {
            el.data = el.created_at.split(" ");
            el.yearData = el.data[0].split("-");
            el.dayData = el.data[1].split(":");
            el.showData = `${el.yearData[1]}月${el.yearData[2]}日 ${el.dayData[0]}: ${el.dayData[1]}`;
          });
        } else if (i == "file") {
          res.result.files = JSON.parse(item);
        } else if (i == "parent_nodes") {
          if (res.result.parent_nodes.length != 0) {
            let parentnodes = "";
            res.result.parent_nodes.forEach((el) => {
              parentnodes = parentnodes + el.user_name + "-" + el.title + " ";
            });
            res.result.parentnodes = parentnodes;
          }
        } else if (i == "next_nodes") {
          if (res.result.next_nodes.length != 0) {
            let nextnodes = "";
            console.log(res.result,888);
            if(res.result.type){
              res.result.next_nodes.forEach((el) => {
                nextnodes = nextnodes + el.user_name + "-" + el.title + " ";
              });
            }else{
              res.result.next_nodes.forEach((el) => {
                nextnodes = nextnodes + el.user_name ;
              });
            }
            res.result.nextnodes = nextnodes;
          }
        }
      }
      taskDetail.value = res.result;
      console.log(taskDetail.value,88999);
    })
    .catch((err) => {
      console.log(err);
    });
};

// 完成任务
const finishTask = () => {
  console.log("测试", taskDetail.value);
  proxy.$_dd.device.notification.prompt({
    message: "请填写当前任务完成百分比",
    title: "每日汇报",
    buttonLabels: ["取消", "确定"],
    onSuccess: function (res) {
      // 调用成功时回调
      if (res.buttonIndex == 1) {
        // 添加数字验证
        const progress = Number(res.value);
        if (isNaN(progress) || progress < 0 || progress > 100) {
          showFailToast("请输入0-100之间的数字");
          return;
        }

        proxy
          .$post("task/post_report", {
            node_id: taskDetail.value.node_info.id,
            progress: progress,
          })
          .then((res) => {
            console.log(res);
            if (res.errcode == 0) {
              showSuccessToast("提交成功");
              const PostData = {
                id: taskDetail.value.id,
                node_id: taskDetail.value.node_info.id,
              };
              getTaskDetailData(PostData);
            } else {
              showFailToast(res.errmsg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    onFail: function (err) {},
  });
};
// 验收任务
const check = (type) => {
  console.log("完成任务");
  console.log(taskDetail.value);
  const postData = {
    node_id: taskDetail.value.node_info.id,
    id: taskDetail.value.id,
    type,
  };
  proxy
    .$post("task/post_confirm", postData)
    .then((res) => {
      console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("提交成功");
        setTimeout(() => {
          closeToast();
          router.push({ name: "index" });
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 展示文件
const showFile = (img) => {
  const file = [img];
  showImagePreview(file);
};

// 报时
const showPopup = () => {
  proxy.$_dd.device.notification.prompt({
    message: "请填写该项目工时（天）",
    title: "工作时长",
    // defaultText: "请输入时长",
    buttonLabels: ["取消", "确定"],
    onSuccess: function (res) {
      // 调用成功时回调
      if(res.value%1!==0){
  showFailToast("请输入整数");
  return;
}
      // console.log(res);
      if (res.buttonIndex == 1) {
        proxy
          .$post("task/post_modify_time", {
            id: taskDetail.value.node_info.id,
            task_id: taskDetail.value.id,
            duration: res.value,
          })
          .then((res) => {
            console.log(res);
            if (res.errcode == 0) {
              showSuccessToast("提交成功");
              const PostData = {
                id: taskDetail.value.id,
                node_id: taskDetail.value.node_info.id,
              };
              getTaskDetailData(PostData);
            } else {
              showFailToast(res.errmsg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    onFail: function (err) {},
  });
};
</script>

<style lang="scss" scoped>
.content {
  padding: 26px;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  box-sizing: border-box;
}

.title {
  display: flex;
  justify-content: space-between;
  font-size: 17px;
  font-weight: 550;
  line-height: 22px;
}

.desc {
  margin-top: 26px;
  font-size: 15px;
  line-height: 22px;
  color: #747677;
}

.file {
  margin-top: 18px;
  .fileItem {
    display: flex;
    padding: 6px 8px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: #ebf5ff;
    margin-top: 8px;

    img {
      margin-right: 8px;
      width: 45px;
      height: 45px;
      border-radius: 4px;
    }

    a {
      color: #747677;
      width: 60%;
      line-height: 45px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.buts {
  margin-top: 46px;
  .but {
    display: inline-block;
    margin-top: 12px;
    margin-right: 10px;
    padding: 0 16px;
    border: 1px solid #eaebed;
    border-radius: 17px;
    line-height: 34px;

    .icon {
      margin-right: 6px;
    }
  }

  .start {
    margin-right: 26px;
  }
}

.task-flow {
  margin-top: 66px;
  position: relative;

  p {
    text-align: center;
    color: #a2a3a5;
  }

  .btns {
    position: fixed;
    left: 0;
    bottom: 70px;
    width: 100%;
    display: flex;
    justify-content: space-around;
  }

  .bt {
    padding: 13px;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    border-radius: 24px;
    display: inline-block;
    background-color: #fff;
  }

  .btn {
    position: fixed;
    left: 50%;
    bottom: 70px;
    transform: translateX(-50%);
    margin-top: 32px;
    padding: 13px;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    border-radius: 24px;
    display: inline-block;
    background-color: #fff;
    // box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);

    .icon {
      margin-right: 8px;
    }
  }

  .sblue {
    background-color: #007fff;
    color: #fff;
  }
  .sred {
    background-color: #ff642b;
    color: #fff;
  }

  .swhite {
    background-color: #fff;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    -webkit-box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    -moz-box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    -ms-box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    // border: .5px solid #ececec;
    z-index: 10;
  }
}
</style>