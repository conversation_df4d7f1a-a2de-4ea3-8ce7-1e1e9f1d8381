import vue from "@vitejs/plugin-vue";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";
import AutoImport from "unplugin-auto-import/vite";

import path from "path";
function _resolve(dir) {
  return path.resolve(__dirname, dir);
}
export default {
  server: {
    // host:"**********",
    host:"**********",
    port: 8000,
    // proxy: {},
  },
  resolve: {
    alias: {
      "@": _resolve("src"),
    },
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
  },
  plugins: [
    vue(),
    Components({
      resolvers: [
        VantResolver({
          importStyle: false,
        }),
      ],
      dirs: ["src/components"],
      extensions: ["vue"],
      // dts: 'src/components.d.ts'
    }),
    AutoImport({
      imports: ["vue", "vue-router", "pinia"],
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@use "src/assets/styles/var.scss";',
      },
    },
  },
};
