<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset>
        <van-field
          v-model="title"
          label="分组名称"
          right-icon="arrow"
          placeholder="未设置"
          input-align="right"
          :rules="[{ required: true, message: '请输入分组名称' }]"
        />
      </van-cell-group>
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit"
        >提交</van-button
      >
    </van-action-bar>
  </van-form>
</template>
    
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { showSuccessToast, showFailToast, closeToast } from "vant";
import { useRouter } from "vue-router";
// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 分组名称
const title = ref("");

// 提交分组
const submit = () => {
  proxy
    .$post("group/post_add", {
      title: title.value,
    })
    .then((res) => {
      if (res.errcode == 0) {
        showSuccessToast("添加成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
    
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}

::v-deep .van-field__error-message {
  text-align: right;
}
</style>