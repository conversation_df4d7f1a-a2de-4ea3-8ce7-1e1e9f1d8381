<template>
  <div class="header">
    <div
      class="left tab"
      :class="postData.is_all == 0 ? 'active' : ''"
      @click="switchHeaderTab(0)"
    >
      待完成 {{ doneNum }}
    </div>
    <div
      class="right tab"
      :class="postData.is_all == 1 ? 'active' : ''"
      @click="switchHeaderTab(1)"
    >
      全部
    </div>
  </div>
  <div class="content">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="taskflowList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-if="taskflowList.length >= 1">
        <template v-for="(el, index) in taskflowList" :key="index">
          <div class="sort-item">
            <div class="left" @click="goDetail(el.id)">
              <div class="title">
                <template v-if="!el.immediately">
                  <img :src="`/icons/lv${el.priority}.png`" alt="" />{{
                    el.title
                  }}
                </template>
                <template v-else>
                  <img src="/icons/fire.png" alt="" />{{ el.title }}
                </template>
              </div>
              <div class="line">
                <div class="img">
                  <template v-if="el.avatar">
                    <img :src="el.avatar" alt="" />
                  </template>
                  <template v-else>
                    <div class="block">{{ el.user_name[0] }}</div>
                  </template>
                </div>
                <div class="name">{{ el.user_name }}</div>
                <div class="time">{{ el.created_content }}</div>
              </div>
              <div class="type">
                {{ el.group_title }}
              </div>
            </div>
            <template v-if="el.status == 0">
              <div class="icon" @click="showPopup(el)">
                <van-icon name="ellipsis" />
              </div>
            </template>
            <template v-else-if="el.status == 1">
              <div class="icon" :style="{ fontSize: '13px' }">已完成</div>
            </template>
            <template v-else-if="el.status == 2">
              <div class="icon" :style="{ fontSize: '13px',color:'#ffac91' }">已废弃</div>
            </template>
          </div>
        </template>
      </template>
      <template v-else-if="showDesc && taskflowList.length == 0">
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </van-list>
  </div>
  <van-action-bar class="bar">
    <van-button class="add" type="primary" @click="goAdd">新增任务</van-button>
  </van-action-bar>
</template>
      
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

// 路由跳转
const route = useRoute();
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 接口配置数据
let postData = ref({
  page: 0,
  per_page: 10,
  is_all: 0,
});
// 头部 tab 数字提示
let doneNum = ref(0);
// 数据列表
let taskflowList = ref([]);
// 列表总数
let total = ref(0);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无任务");
// 列表加载
const loading = ref(false);
const finished = ref(false);

onBeforeMount(() => {
  // 获取任务分配列表
  // getList();
  // console.log(route.query);
  // if (route.query.index == 0) {
  //   postData.value.is_all = 1;
  //   onLoad();
  // } else {
  //   postData.value.is_all = 0;
  //   onLoad();
  // }
  // console.log("-----是否加载------");
  // console.log(postData.value.is_all);
  // onLoad();
});

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("task/get_ls", postData.value)
    .then((res) => {
      // console.log(res.result.data);
      if (postData.value.is_all == 0) {
        doneNum.value = res.result.total;
      }
      res.result.data.forEach((el) => {
        const dates = el.created_at.split(" ");
        const date = dates[0];
        const time = dates[1];
        const dateItems = date.split("-");
        const timeItmes = time.split(":");
        // console.log("---------------");
        // console.log(dateItems, timeItmes);
        el.created_content = `创建于${dateItems[1]}月${dateItems[2]}日 ${timeItmes[0]}:${timeItmes[1]}`;
        taskflowList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;
      loading.value = false;
      // 加载完毕
      if (taskflowList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 列表加载
const onLoad = () => {
  // console.log("需要再次加载数据");
  postData.value.page = postData.value.page + 1;
  getList();
};

// 点击添加数据
const goAdd = () => {
  router.push({ name: "TaskAllocationAdd" });
};

// 点击跳转详情
const goDetail = (id) => {
  router.push({ name: "TaskAllocationInfo", query: { id } });
};

// 点击 tab 切换
const switchHeaderTab = (num) => {
  // console.log(num);
  postData.value.is_all = num;
  postData.value.page = 0;
  taskflowList.value = [];
  onLoad();
};

const showPopup = (el) => {
  proxy.$_dd.device.notification.actionSheet({
    title: el.title, //标题
    cancelButton: "取消", //取消按钮文本
    otherButtons: ["修改", "删除"],
    onSuccess: function (result) {
      console.log(result);
      if (result.buttonIndex == 0) {
        // 修改
        router.push({
          name: "TaskAllocationEdit",
          query: { id: el.id },
        });
      } else if (result.buttonIndex == 1) {
        delData(el.id);
      }
      //onSuccess将在点击button之后回调
      /*{
            buttonIndex: 0 //被点击按钮的索引值，Number，从0开始, 取消按钮为-1
        }*/
    },
    onFail: function (err) {},
  });
};
// 删除
const delData = (id) => {
  showConfirmDialog({
    title: "删除",
    message: "确定删除此任务么?",
  })
    .then(() => {
      proxy
        .$post("task/post_del", { id })
        .then((res) => {
          console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("删除成功");
            postData.value.page = 0;
            taskflowList.value = [];
            onLoad();
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    })
    .catch(() => {
      // on cancel
    });
};
</script>
      
<style lang="scss" scoped>
.header {
  display: flex;
  height: 44px;
  line-height: 44px;
  background-color: #fff;
  text-align: center;

  div {
    flex: 0 0 50%;
    font-size: 14px;
  }

  .active {
    position: relative;
    font-weight: 550;
  }

  .active::after {
    content: "";
    display: inline-block;
    position: absolute;
    left: 50%;
    bottom: 4px;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    border-radius: 1px;
    background-color: #000;
  }
}

.content {
  padding-bottom: 75px;
  padding-top: 16px;

  .sort-item {
    display: flex;
    justify-content: space-between;
    margin-left: 5%;
    margin-top: 12px;
    padding: 16px;
    width: 90%;
    // height: 79px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .left {
      .title {
        display: flex;

        font-size: 17px;
        line-height: 22px;
        margin-bottom: 16px;

        img {
          width: 22px;
          height: 22px;
          margin-right: 10px;
        }
      }

      .line {
        margin-bottom: 16px;
        font-size: 13px;
        display: flex;
        color: #a2a3a5;
        line-height: 21px;

        .img {
          margin-right: 6px;
          width: 21px;
          height: 21px;
          border-radius: 3px;
          background-color: #007fff;
          color: #fff;
          text-align: center;

          img {
            width: 100%;
            height: 100%;
            border-radius: 3px;
          }
        }

        .name {
          position: relative;
          padding-right: 12px;
        }

        .name::after {
          position: absolute;
          top: 3px;
          right: 0;
          content: "";
          display: block;
          height: 15px;
          width: 1px;
          background-color: #eaebed;
        }

        .time {
          margin-left: 12px;
        }
      }
      .type {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 6px;
        font-size: 10px;
        line-height: 17px;
        background-color: #e0efff;
        color: #007fff;
      }
    }
    .icon {
      color: #c8c8c9;
    }
  }
}

.bar {
  height: 60px;

  .add {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>