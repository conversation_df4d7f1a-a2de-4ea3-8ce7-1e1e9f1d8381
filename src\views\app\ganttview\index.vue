<template>
  <section class="content" ref="content" @scroll="scrollEvent($event)">
    <div class="header">
      <div class="left" @click="selectPerson">
        部门/人员 <van-icon name="arrow-down" />
        <span>{{ selectDatalist.selectedCount?selectDatalist.departments.length?selectDatalist.departments[0].name:selectDatalist.users.length?selectDatalist.users[0].name:"":""}}</span>
      </div>
      <div class="right">
        <div class="options">
          <span
            >{{ headerTimeYear }}年{{ headerTimeDay }}月
            <van-icon name="arrow-down" />
          </span>
        </div>
        <div class="line"></div>
        <div class="today">
          <span @click="switchToday">今天</span>
          <van-icon
            class="arrow-left"
            @click="switchData(-1)"
            name="arrow-left"
          />
          <van-icon class="arrow-right" @click="switchData(1)" name="arrow" />
        </div>
      </div>
    </div>
    <div class="dateNum" ref="dateNum">
      <template v-for="(k, x) in days" :key="x">
        <div class="day">{{ k }}</div>
      </template>
    </div>
    <template v-if="isShowList">
      <template v-for="(el, index) in userTaskList" :key="index">
        <div
          class="userName item"
          :class="index == 0 ? 'firstName' : ''"
          @click="switchList(index)"
        >
          <div class="left">
            <van-icon
              class="arrow-icon"
              :class="el.isshow ? 'active-arrow' : ''"
              name="arrow"
            />
            <div class="img">
              <template v-if="el.img">
                <img :src="el.img" alt="" />
              </template>
              <template v-else>
                <div class="icon">{{ el.name[0] }}</div>
              </template>
            </div>
            <div class="name">{{ el.name }}</div>
          </div>
          <div class="right">{{ el.num }}条记录</div>
        </div>
        <template v-if="el.isshow">
          <div class="taskList">
            <div class="item">
              <!-- <template v-for="(k, x) in days" :key="x">
                <template v-for="(j, ix) in k" :key="ix">
                  <div
                    class="day"
                    :style="{ height: `${44 * el.taskList.length}px` }"
                  ></div>
                </template>
              </template> -->
              <template v-for="(k, x) in days" :key="x">
                <div
                  class="day"
                  :style="{ height: `${44 * el.task_list.length}px` }"
                ></div>
                <!-- <template v-for="(j, ix) in k" :key="ix">
              <div class="day">{{ j }}</div>
            </template> -->
              </template>
            </div>
            <div class="taskBlocks">
              <!-- <template
              v-for="(item, inx) in el.taskList"
              :key="inx"
            > -->
              <div
                v-for="(item, inx) in el.task_list"
                :key="inx"
                ref="setItemRef"
                class="taskBlock"
                :style="{
                  width: `${50 * item.days}px`,
                  marginLeft: `${50 * (item.begin - 1)}px`,
                }"
                @click="goDetail(item.task_id)"
              >
                {{ item.title }}
              </div>
              <!-- </template> -->
            </div>
          </div>
        </template>
      </template>
    </template>
    <template v-else>
      <van-empty
        image="/icons/acquiesce.png"
        image-size="250"
        description="暂无内容"
        :style="{ marginTop: '150px' }"
      />
    </template>
  </section>
</template>
  
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  onUnmounted,
  watch,
  onBeforeUpdate,
  onUpdated,
  getCurrentInstance,
} from "vue";
import { useRouter } from "vue-router";
import { useLoginStore } from "@/store/dingLogin";
import { number } from "echarts";

const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 页面元素
const content = ref(null);
const dateNum = ref(null);
const setItemRef = ref(null);

// 相关数据信息
let userTaskList = ref();

// 是否展示数据
let isShowList = ref(false);

// 点击跳转详情页
const goDetail = (id) => {
  console.log(id);
  router.push({ name: "TaskAllocationInfo", query: { id: id } });
};

// let itemRefs = ref([]);
// const setItemRef = (el) => {
//   console.log("测试");
//   if (el) {
//     itemRefs.push(el);
//   }
// };

// onBeforeUpdate(() => {
//   itemRefs.value = [];
// });

// onUpdated(() => {
//   // console.log(setItemRef.value);
//   setItemRef.value.forEach((el) => {
//     console.log(el.scrollLeft);
//     // let item = window.getComputedStyle(el, ":before");
//     // console.log("-----");
//     // console.log(item.backgroundColor);
//     // console.log(item.width);
//     // item.width='50px'
//     // item.backgroundColor = "rgb(100, 128, 0)";
//     // itemRefs.value.push(item);

//     itemRefs.value.push(el);
//   });
// });

// watch(
//   () => itemRefs.value.map((item) => item.clientWidth),
//   (newVal, oldVal) => {
//     // console.log(`checked values changed from ${oldVal} to ${newVal}`);
//   }
// );

// 头部日期
const headerTimeYear = ref("");
const headerTimeDay = ref("");

// 人员
const userlst = ref();

// 头部查看类型
// const headerType = ref(1);

// 当天日期
// const todayDay = ref(1);

// 日期集合
const days = ref(0);

// 滚轴
let pageTop = ref(0);

// 时间选项集合
// const dayType = reactive([
//   {
//     id: 1,
//     title: "按月查看",
//   },
//   {
//     id: 2,
//     title: "按年查看",
//   },
// ]);

// 当前时间选项
const selectDayType = reactive({
  id: 1,
  title: "按月查看",
});

// 页面首次加载
onMounted(() => {
  switchToday();
  // console.log(dateNum.value.scrollLeft);
  // scrollTop.value = dateNum.value.scrollLeft;
  window.addEventListener("scroll", scrollListenerHandler);
});

// 页面卸载
onUnmounted(() => {
  window.removeEventListener("scroll", scrollListenerHandler);
});

// 获取列表数据
const getList = () => {
  let mouth = ref(0);
  if (headerTimeDay.value <= 9) {
    mouth.value = `0${headerTimeDay.value}`;
  } else {
    mouth.value = headerTimeDay.value;
  }
  proxy
    .$get("datav/get_bar_chart", {
      year: headerTimeYear.value,
      month: mouth.value,
      userlst: JSON.stringify(userlst.value),
    })
    .then((res) => {
      console.log(res.result);
      userTaskList.value = res.result;
      if (userTaskList.value.length != 0) {
        userTaskList.value.forEach((el) => {
          el.isshow = true;
          el.task_list.forEach((k) => {
            k.begin = k.begin_time.split("-")[2];
          });
        });
        isShowList.value = true;
      } else {
        isShowList.value = false;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

const scrollListenerHandler = () => {
  const scrollTop = document.documentElement.scrollTop;
  // console.log("监听");
  // console.log(scrollTop);
  // pageTop.value = scrollTop;
};

// watch(pageTop, (newValue, oldValue) => {
//   console.log("监听到了txt的变化");
//   console.log(newValue, oldValue);
//   // dateNum.style.background = "#1e1e1e";
// });

const scrollEvent = (e) => {
  // console.log("横向监听到了");
  // console.log(e.target.scrollLeft);
  // console.log(dateNum.value.style);
  dateNum.value.style.left = `-${e.target.scrollLeft}px`;
};

// const workScrollEvent = (e) => {
//   console.log("横向监听到了");
// };

// 处理时间
const nowDate = (now) => {
  const year = now.getFullYear(); //年份
  const month = now.getMonth() + 1; //月份（0-11）
  const date = now.getDate(); //天数（1到31）
  headerTimeYear.value = year;
  headerTimeDay.value = month;
};

// 切换时间 (上月/下月)
const switchData = (step) => {
  // console.log(step);
  headerTimeDay.value = headerTimeDay.value + step;
  if (headerTimeDay.value == 0) {
    headerTimeDay.value = 12;
    headerTimeYear.value = headerTimeYear.value - 1;
  } else if (headerTimeDay.value == 13) {
    headerTimeDay.value = 1;
    headerTimeYear.value = headerTimeYear.value + 1;
  }
  // 获取当月天数 curretMonthDayCount
  const curretMonth = new Date(headerTimeYear.value, headerTimeDay.value, 0);
  const curretMonthDayCount = curretMonth.getDate();
  // console.log(curretMonthDayCount);
  days.value = curretMonthDayCount;

  getList();
};

const switchToday = () => {
  // 获取时间
  const today = new Date();
  // console.log(today);
  // 处理时间
  nowDate(today);
  // 获取当月天数 curretMonthDayCount
  const curretMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  const curretMonthDayCount = curretMonth.getDate();
  days.value = curretMonthDayCount;
  // 获取列表数据
  getList();
};

// 切换列表是否展示
const switchList = (index) => {
  userTaskList[index].isshow = !userTaskList[index].isshow;
};
let selectDatalist=ref({
  selectedCount:0
});
// 人员筛选项
const selectPerson = () => {
  let selectUsers = ref();
  // if (directorLsts.value && directorLsts.value.length > 0) {
  //   selectUsers = directorLsts.value.map((item) => {
  //     return item.userid;
  //   });
  // }

  // console.log(selectUsers);
  // console.log("--------------");
  // console.log(useLoginStore().appid);
  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: true,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: selectUsers, //已选用户
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      console.log(data,555);
      // 选择联系人或部门成功后的回调函数
      if(data.users)
      userlst.value = {
        departments: data.departments.map((item) => { 
          return { name: item.name, id: item.id,number: item.number };
}),
        users: data.users.map((item) => {
          return { avatar: item.avatar, name: item.name, userid: item.emplId };
        }),
      };
      console.log(userlst.value);
      selectDatalist.value=data
      if()
      // 获取列表数据
      getList();
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
      userlst.value = {};
      getList();
    },
  });
};
</script>
  
<style lang="scss" scoped>
.content {
  background: #f2f3f4;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  overflow-x: auto;
  overflow-y: auto;
  white-space: nowrap;
}

.header {
  position: fixed;
  right: 0;
  top: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  width: 100%;
  height: 54px;
  line-height: 54px;
  background-color: #f9f9f9;
  box-sizing: border-box;
  z-index: 999;

  .left {
    color: #0071ff;
  }

  .right {
    display: flex;

    .line {
      margin: 16px 8px;
      width: 1px;
      height: 22px;
      background: #e5e6e8;
    }

    .today {
      span {
        color: #9e9fa1;
      }
    }

    .arrow-down {
      margin-left: 6px;
    }

    .arrow-left {
      margin-left: 12px;
    }

    .arrow-right {
      margin-left: 12px;
    }
  }
}

.dateNum {
  position: fixed;
  left: 0;
  top: 0;
  margin-top: 54px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  color: #747677;
  z-index: 999;

  .day {
    background-color: #fff;
    display: inline-block;
    width: 50px;
  }
}

.item {
  display: inline-block;
  background: #fff;
  // width: 15000px;
  // height: 100px;
}

.userName {
  position: sticky;
  left: 0;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding: 0 12px;
  width: 100%;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;

  .left {
    display: flex;

    .arrow-icon {
      line-height: 44px;
      font-size: 21px;
      transition: all 0.2s;
    }

    .active-arrow {
      transform: rotate(90deg);
    }

    .img {
      margin-left: 12px;
      margin-top: 8px;
      width: 28px;
      height: 28px;
      border-radius: 6px;
      background-color: #3662ec;
      color: #fff;
      line-height: 28px;
      text-align: center;
    }

    .name {
      margin-left: 8px;
    }
  }

  .right {
    color: #a2a3a5;
  }
}

.firstName {
  margin-top: 108px;
}

.taskList {
  position: relative;
  .day {
    background-color: #fff;
    display: inline-block;
    width: 50px;
    // height: 44px;
    text-align: center;
    border-right: 2px solid #f8f8f9;
    box-sizing: border-box;
  }
  .taskBlocks {
    position: absolute;
    top: 6px;
    z-index: 99;
    .taskBlock {
      position: relative;
      margin-top: 12px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      background: rgba(0, 176, 66, 0.12);
      text-align: center;
    }

    // .taskBlock::before {
    //   display: block;
    //   content: "";
    //   position: absolute;
    //   top: 0;
    //   width: 32px;
    //   height: 32px;
    //   background-color: green;
    // }

    .arrow-task {
      // position: fixed;
      width: 32px;
      height: 32px;
      background-color: #c5ecd4;
      border-radius: 4px;
      z-index: 99;
    }
  }

  .taskBlock:first-child {
    margin-top: 0;
  }
}
</style>