<template>
  <div class="content">
    <div class="logo">
      <img src="/icons/logo.png" alt="" />
      <div class="app-name">云一工作 {{ set }}</div>
    </div>
    <!-- <div class="btns">
      <template v-for="(el, index) in btns" :key="index">
        <div class="btn" @click="goBtnPage(el.goName)">
          <van-icon class="licon" :name="el.icon" />
          <span>{{ el.title }}</span>
          <van-icon class="ricon" name="arrow" />
        </div>
      </template>
    </div> -->
    <div class="btns">
      <div class="btn">
        <span>授权设备</span>
        <div class="ricon">{{ total }}台</div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
// 选项按钮
const btns = reactive([
  {
    title: "更新日志",
    goName: "about",
  },
]);
// 设置
const set = import.meta.env.VITE_APP_VERSION;

// 接口
const { proxy } = getCurrentInstance();

let total = ref(0);

// 页面首次加载
onMounted(() => {
  getDevice();
});

const getDevice = () => {
  proxy
    .$get("sys/get_authorized_device")
    .then((res) => {})
    .catch((err) => {
      if (res.errcode == 0) {
        total.value = res.result;
      }
    });
};
</script>
  
<style lang="scss" scoped>
.content {
  overflow: hidden;
}
.logo {
  margin-top: 26px;
  text-align: center;

  img {
    width: 84px;
    height: 84px;
  }

  .app-name {
    font-size: 17px;
    line-height: 20px;
  }
}

.btns {
  margin-top: 26px;

  .btn {
    display: flex;
    align-items: center;
    position: relative;
    margin-left: 5%;
    width: 90%;
    height: 56px;
    border-radius: 6px;
    background-color: #fff;
    span {
      margin-left: 16px;
      font-size: 17px;
    }

    .ricon {
      position: absolute;
      right: 16px;
      color: #a2a3a5;
    }
  }
}
</style>