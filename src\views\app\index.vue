<template>
  <template v-if="isShowMenu">
    <div class="box" ref="content">
      <div class="content">
        <template v-for="(el, index) in showMenu" :key="index">
          <div class="app-card">
            <div class="title">{{ el.title }}</div>
            <div class="app-items" justify="space-between">
              <template v-for="(item, inx) in el.child" :key="inx">
                <div class="app-item" @click="goAppPage(item.goName)">
                  <div class="img" :style="{ background: item.bgc }">
                    <img :src="item.icon" alt="" />
                  </div>
                  <p>{{ item.title }}</p>
                </div>
              </template>
              <template v-if="el.child.length == 3">
                <div class="occap" :style="{ width: '50px' }"></div>
              </template>
              <template v-if="el.child.length == 2">
                <div class="occap" :style="{ width: '50px' }"></div>
                <div class="occap" :style="{ width: '50px' }"></div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
  </template>
  <template v-else>
    <van-empty
      image="/icons/acquiesce.png"
      image-size="250"
      description="暂无权限"
    />
  </template>
</template>

<script setup>
import { ref, reactive, onBeforeMount, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useLoginStore } from "@/store/dingLogin";

// 路由跳转
const router = useRouter();

// 点击相关页面
const goAppPage = (name) => {
  console.log(name);
  router.push({ name });
};

// 记录视口高度
const viewportHeight = ref(0);
const content = ref(null);
let appMenu = ref([
  {
    title: "常用管理",
    child: [
      {
        id: 1,
        title: "任务分配",
        icon: "/icons/TaskAllocation.png",
        bgc: "#e5f1ff",
        goName: "TaskAllocation",
      },
    ],
  },
  {
    title: "基础配置",
    child: [
      {
        id: 2,
        title: "任务分组",
        icon: "/icons/TaskGroup.png",
        bgc: "#eeeff3",
        goName: "TaskGroup",
      },
      {
        id: 7,
        title: "岗位管理",
        icon: "/icons/post.png",
        bgc: "#e5f5eb",
        goName: "post",
      },
      // {
      //   id: 3,
      //   title: "任务流",
      //   icon: "/icons/TaskFlow.png",
      //   bgc: "#ffefe9",
      //   goName: "TaskFlow",
      // },
      // {
      //   id: 4,
      //   title: "同步禅道",
      //   icon: "/icons/ZenTao.png",
      //   bgc: "#e5f6eb",
      //   goName: "ZenTao",
      // },
    ],
  },
  {
    title: "数据统计",
    child: [
      {
        id: 7,
        title: "工作清单",
        icon: "/icons/WorkList.png",
        bgc: "#e5f5eb",
        goName: "WorkList",
      },
      {
        id: 8,
        title: "状态看板",
        icon: "/icons/StatusKanban.png",
        bgc: "#fff3e5",
        goName: "StatusKanban",
      },
      {
        id: 9,
        title: "日程分布",
        icon: "/icons/GanttView.png",
        bgc: "#ecfbfb",
        goName: "GanttView",
      },
      {
        id: 5,
        title: "任务统计",
        icon: "/icons/TaskStatistics.png",
        bgc: "#ecfdf9",
        goName: "TaskStatistics",
      },
      // {
      //   title: "数据报表",
      //   icon: "/icons/DataReport.png",
      //   bgc: "#e5efff",
      // },
    ],
  },
  {
    title: "其他",
    child: [
      {
        id: 6,
        title: "权限管理",
        icon: "/icons/Authority.png",
        bgc: "#e5f1ff",
        goName: "Authority",
      },
      // {
      //   id: 6,
      //   title: "审计日志",
      //   icon: "/icons/auditlog.png",
      //   bgc: "#ecfdf9",
      //   goName: "auditLog",
      // },
      {
        id: 2,
        title: "系统配置",
        icon: "/icons/set.png",
        bgc: "#eeeff3",
        goName: "set",
      },
    ],
  },
]);

let showMenu = ref([]);
let isShowMenu = ref(true);

onMounted(() => {
  const temviewportHeight = document.documentElement.clientHeight;
  viewportHeight.value = temviewportHeight;
  // console.log(viewportHeight.value);
  content.value.style.height = `${viewportHeight.value - 80}px`;
  // console.log("-------------------");
  // console.log(useLoginStore().loginData.permission);
  // const permission = useLoginStore().loginData.permission;
  // if (permission.all_action == 1) {
  showMenu.value = appMenu.value;
  // } else {
  //   appMenu.value.forEach((el) => {
  //     let itemNum = 0;
  //     let item = JSON.parse(JSON.stringify(el));
  //     item.child = [];
  //     el.child.forEach((k) => {
  //       permission.actions.forEach((j) => {
  //         if (k.id == j.id) {
  //           item.child.push(k);
  //           itemNum = itemNum + 1;
  //         }
  //       });
  //     });
  //     if (item.child.length != 0) {
  //       showMenu.value.push(item);
  //     }
  //   });
  //   if (showMenu.value.length == 0) {
  //     isShowMenu.value = false;
  //   }
  // }
});
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  padding-bottom: 75px;
  overflow: hidden;
}
.content {
  height: 100%;
  // margin-left: 12px;
  margin: 16px 12px;
  // margin-bottom: 75px;
  // width: 96%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  padding: 0 12px;
}

.app-card {
  margin-top: 26px;

  .title {
    font-size: 14px;
    color: rgba(23, 26, 29, 0.4);
  }

  .app-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;

    .app-item {
      margin-top: 16px;
      .img {
        width: 50px;
        height: 50px;
        border-radius: 8px;

        img {
          margin: 10px;
          width: 30px;
          height: 30px;
        }
      }
      p {
        width: 50px;
        text-align: center;
      }
    }
  }
}
</style>
  