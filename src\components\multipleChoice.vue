<template>
  <van-field
    v-model="showvalue"
    :label="props.label"
    placeholder="请选择"
    input-align="right"
    is-link
    readonly
    @click="openSelect"
  >
    <template #left-icon v-if="props.is_second">
      <img class="second" src="/icons/second.png" alt="" />
    </template>
  </van-field>

  <van-popup
    class="van-popup"
    v-model:show="showBottom"
    position="bottom"
    :style="{ height: '60%' }"
    round
    closeable
    close-icon-position="top-left"
  >
    <div class="header">
      <div class="title">{{ props.label }}</div>
      <div class="ok" @click="offPopup">确定</div>
    </div>
    <div class="selectItmes">
      <template v-if="list">
        <van-checkbox-group v-model="checked">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in list"
              clickable
              :key="item.id"
              :title="item[props.remote.label]"
              @click="toggle(index)"
            >
              <template #icon>
                <van-checkbox
                  class="selectIcon"
                  :name="item"
                  :ref="(el) => (checkboxRefs[index] = el)"
                  @click.stop
                />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </template>
      <template v-else>
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </div>
  </van-popup>
</template>
  
  <script setup>
import {
  ref,
  reactive,
  onBeforeUpdate,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  showToast,
} from "vant";
import { useRouter } from "vue-router";

// 接口
const { proxy } = getCurrentInstance();
// emit 发送数据
const emit = defineEmits(["postData"]);

let showvalue = ref();

const props = defineProps({
  label: String,
  is_second: {
    type: Boolean,
    default: false,
  },
  is_remote: {
    type: Boolean,
    default: false,
  },
  remote: Object,
});

const showBottom = ref(false);

// 打开遮罩层
const openSelect = () => {
  showBottom.value = true;
};

const checked = ref([]);
const list = ref();
const checkboxRefs = ref([]);
const toggle = (index) => {
  checkboxRefs.value[index].toggle();
};

onBeforeUpdate(() => {
  checkboxRefs.value = [];
});

onBeforeMount(() => {
  if (props.is_remote) {
    // 远程
    getList();
  }
});

// 恐状态描述
let description = ref("暂无数据");

// 获取数据列表
const getList = () => {
  proxy
    .$get(props.remote.api)
    .then((res) => {
      //   console.log(res);
      if (res.errcode == 0) {
        if (res.result.length != 0) {
          list.value = res.result;
        }
      } else {
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 确认按钮
const offPopup = () => {
  showBottom.value = false;
  let value = "";
  for (let i = 0; i < checked.value.length; i++) {
    let el = checked.value[i];
    if (i == 0) {
      value = value + el[props.remote.label];
    } else {
      value = value + "、" + el[props.remote.label];
    }
  }
  //   console.log("---------------");
  //   console.log(value);
  showvalue.value = value;
  emit("postData", checked.value);
};
</script>
  
<style lang="scss" scoped>
// 二级头
.second {
  width: 24px;
  height: 24px;
}

.van-popup {
  .header {
    position: relative;
    width: 100%;
    height: 52px;
    // background-color: blue;
    line-height: 52px;
    border-bottom: 0.5px solid #eaebed;

    .title {
      text-align: center;
    }

    .ok {
      position: absolute;
      right: 16px;
      top: 0;
    }
  }

  .selectItmes {
    .selectIcon {
      margin-right: 20px;
    }
  }
}
</style>