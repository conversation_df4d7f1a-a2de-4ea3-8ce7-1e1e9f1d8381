<template>
  <div class="content" ref="content">
    <div class="title">{{ taskDetail.title }}</div>
    <div class="desc">{{ taskDetail.content }}</div>
    <div class="file">
      <template v-for="(el, x) in taskDetail.files" :key="x">
        <div class="fileItem" @click="showFile(el)">
          <img src="/icons/Excel.png" alt="" />
          <span class="text">{{ el }}</span>
        </div>
      </template>
    </div>
    <div class="buts">
      <div class="user but">
        <van-icon name="contact" class="icon" />{{ taskDetail.users }}
      </div>
      <div
        class="end but"
        :style="{ color: taskDetail.publish_time ? '#000' : '#a2a3a5' }"
      >
        <van-icon name="notes-o" class="icon" />{{
          taskDetail.publish_time ? taskDetail.publish_time : "上线时间"
        }}
      </div>
      <div class="sort but">
        <van-icon name="label-o" class="icon" />任务分组:
        {{ taskDetail.group_title }}
      </div>
    </div>
    <div class="task-flow">
      <div class="title">任务流程</div>
      <van-divider />
      <template v-for="(item, index) in taskDetail.process" :key="index">
        <p>{{ item.showData }} {{ item.user_name }} {{ item.action }}</p>
      </template>
    </div>
  </div>
</template>
  
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  showImagePreview,
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 记录视口高度
const viewportHeight = ref(0);
const content = ref(null);

// 详情数据信息
let taskDetail = ref({});
let is_operate = ref();
// 发送数据
let PostData = ref();

onMounted(() => {
  // 处理视口
  const temviewportHeight = document.documentElement.clientHeight;
  viewportHeight.value = temviewportHeight;
  // console.log(viewportHeight.value);
  content.value.style.height = `${viewportHeight.value}px`;
  PostData.value = {
    id: route.query.id,
  };
  // 获取详情数据
  getTaskDetailData(PostData.value);
});

// 获取详情数据
const getTaskDetailData = (postData) => {
  proxy
    .$get("task/get_info", postData)
    .then((res) => {
      // console.log(res.result);
      for (let i in res.result) {
        let item = res.result[i];
        // console.log("-----------------------");
        // console.log(item);
        if (i == "flow") {
          res.result.users = `${item.title}，${item.finish}/${item.total}已完成`;
        } else if (i == "publish_time" && item) {
          const dataArr = item.split("-");
          res.result.publish_time = `${dataArr[1]}月${dataArr[2]}日 上线`;
        } else if (i == "process") {
          // console.log(item);
          item.forEach((el) => {
            el.data = el.created_at.split(" ");
            el.yearData = el.data[0].split("-");
            el.dayData = el.data[1].split(":");
            el.showData = `${el.yearData[1]}月${el.yearData[2]}日 ${el.dayData[0]}: ${el.dayData[1]}`;
          });
        } else if (i == "file") {
          res.result.files = JSON.parse(item);
        }
      }
      taskDetail.value = res.result;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 展示文件
const showFile = (img) => {
  //   const file = [img];
  //   showImagePreview(file);
  proxy.$_dd.biz.util.openLink({
    url: img,
    onSuccess: function (res) {
      // 调用成功时回调
      console.log(res);
    },
    onFail: function (err) {
      // 调用失败时回调
      console.log(err);
    },
  });
};
</script>
  
  <style lang="scss" scoped>
.content {
  padding: 26px;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  box-sizing: border-box;
}

.title {
  font-size: 17px;
  font-weight: 550;
  line-height: 22px;
}

.desc {
  margin-top: 26px;
  font-size: 15px;
  line-height: 22px;
  color: #747677;
}

.file {
  margin-top: 26px;
  .fileItem {
    display: flex;
    padding: 6px 8px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: #ebf5ff;

    img {
      margin-right: 8px;
      width: 45px;
      height: 45px;
      border-radius: 4px;
    }

    .text {
      color: #171a1d;
      margin-left: 5%;
      width: 60%;
      line-height: 45px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.buts {
  margin-top: 46px;
  .but {
    display: inline-block;
    margin-right: 40px;
    margin-top: 12px;
    padding: 0 16px;
    border: 1px solid #eaebed;
    border-radius: 17px;
    line-height: 34px;

    .icon {
      margin-right: 6px;
    }
  }

  .start {
    margin-right: 26px;
  }
}

.task-flow {
  margin-top: 66px;

  p {
    text-align: center;
    color: #a2a3a5;
  }

  .btn {
    position: fixed;
    left: 50%;
    bottom: 70px;
    transform: translateX(-50%);
    margin-top: 32px;
    padding: 13px;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    border-radius: 24px;
    display: inline-block;

    .icon {
      margin-right: 8px;
    }
  }

  .sblue {
    background-color: #007fff;
    color: #fff;
  }

  .swhite {
    padding: 13px;
    display: flex;
    background-color: #000;
    color: #fff;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.16);
    z-index: 10;
    line-height: 24px;

    .finish {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }
  }
}
</style>