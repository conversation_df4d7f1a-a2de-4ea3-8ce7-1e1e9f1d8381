import { onMounted, onUnmounted, ref } from 'vue';
import { throttle } from 'underscore'
// export default function useScroll(reachBottomCB) {
//   const scrollListenerHandler = window.addEventListener('scroll', () => {
//     const clientHeight = document.documentElement.clientHeight;
//     const scrollTop = document.documentElement.scrollTop;
//     const scrollHeight = document.documentElement.scrollHeight;
//     if (scrollHeight <= clientHeight + scrollTop) {
//       if (reachBottomCB) reachBottomCB()
//     }
//   })

//   onMounted(() => {
//     window.addEventListener('scroll', scrollListenerHandler)
//   })

//   onUnmounted(() => {
//     window.removeEventListener('scroll', scrollListenerHandler)
//   })
// }

export default function useScroll() {
    // const isReachBottom = ref(false)  // 定义一个是否已经滚动到底部的布尔值
    const scrollListenerHandler = () => {
        // const clientHeight = document.documentElement.clientHeight;
        const scrollTop = document.documentElement.scrollTop;
        // const scrollHeight = document.documentElement.scrollHeight;
        // // 滚动到了底部
        // if (scrollHeight <= clientHeight + scrollTop) {
        //     console.log("滚动到底部了")
        //     isReachBottom.value = true
        // }
        // console.log('监听');
        // console.log(scrollTop);
    }

    // 页面首次加载
    onMounted(() => {
        window.addEventListener('scroll', scrollListenerHandler)   
    })

    // 页面卸载
    onUnmounted(() => {
        window.removeEventListener('scroll', scrollListenerHandler)
    })

    return { scrollTop } 
}

