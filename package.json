{"name": "vite", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"axios": "^1.4.0", "dingtalk-jsapi": "^3.0.20", "echarts": "^5.4.2", "nprogress": "^0.2.0", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^3.1.0", "underscore": "^1.13.6", "vant": "^4.0.0", "vue": "^3.2.45", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "sass": "^1.62.1", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.22.11", "vite": "^3.2.4"}, "license": "ISC"}