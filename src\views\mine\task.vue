<template>
  <section class="content" ref="content" @scroll="scrollEvent($event)">
    <div class="header">
      <div class="left">{{ headerTimeYear }}年{{ headerTimeDay }}月</div>
      <div class="right">
        <div class="options">
          <span>{{ selectDayType.title }}</span>
          <!-- <van-icon class="arrow-down" name="arrow-down" /> -->
        </div>
        <div class="line"></div>
        <div class="today">
          <span @click="switchToday">今天</span>
          <van-icon
            class="arrow-left"
            @click="switchData(-1)"
            name="arrow-left"
          />
          <van-icon class="arrow-right" @click="switchData(1)" name="arrow" />
        </div>
      </div>
    </div>
    <div class="dateNum" ref="dateNum">
      <div class="dateContainer" :style="{ width: `${50 * days}px` }" @scroll="scrollEvent($event)">
        <template v-for="day in daysList" :key="day">
          <div class="day">{{ day }}</div>
          <!-- <template v-for="(j, ix) in k" :key="ix">
              <div class="day">{{ j }}</div>
            </template> -->
        </template>
      </div>
    </div>
    <template v-if="isShowList">
      <template v-for="(el, index) in userTaskList" :key="index">
        <div
          class="userName item"
          :class="index == 0 ? 'firstName' : ''"
          @click="switchList(index)"
        >
          <div class="left">
            <van-icon
              class="arrow-icon"
              :class="el.isshow ? 'active-arrow' : ''"
              name="arrow"
            />
            <div class="img">
              <template v-if="el.img">
                <img :src="el.img" alt="" />
              </template>
              <template v-else>
                <div class="icon">{{ el.name[0] }}</div>
              </template>
            </div>
            <div class="name">{{ el.name }}</div>
          </div>
          <div class="right">{{ el.num }}条记录</div>
        </div>
        <template v-if="el.isshow">
          <div class="taskList">
            <div class="item">
              <!-- <template v-for="(k, x) in days" :key="x">
                <template v-for="(j, ix) in k" :key="ix">
                  <div
                    class="day"
                    :style="{ height: `${44 * el.taskList.length}px` }"
                  ></div>
                </template>
              </template> -->
              <template v-for="day in daysList" :key="day">
                <div
                  class="day"
                  :style="{ height: `${44 * el.task_list.length}px` }"
                ></div>
                <!-- <template v-for="(j, ix) in k" :key="ix">
              <div class="day">{{ j }}</div>
            </template> -->
              </template>
            </div>
            <div class="taskBlocks">
              <!-- <template
              v-for="(item, inx) in el.taskList"
              :key="inx"
            > -->
              <div
                v-for="(item, inx) in el.task_list"
                :key="inx"
                ref="setItemRef"
                class="taskBlock"
                :style="{
                  width: `${50 * item.days}px`,
                  marginLeft: `${50 * (item.begin - 1)}px`,
                }"
                @click="goDetail(item)"
              >
                {{ item.title }}
              </div>
              <!-- </template> -->
            </div>
          </div>
        </template>
      </template>
    </template>
    <template v-else>
      <van-empty
        image="/icons/acquiesce.png"
        image-size="250"
        description="暂无内容"
        :style="{ marginTop: '150px' }"
      />
    </template>
  </section>
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  onBeforeMount,
  onMounted,
  onUnmounted,
  watch,
  onBeforeUpdate,
  onUpdated,
  getCurrentInstance,
} from "vue";
// import useScroll from "@/untils/useScroll";
import { useRouter } from "vue-router";

const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 页面元素
const content = ref(null);
const dateNum = ref(null);
const dateContainer = ref(null);
const setItemRef = ref(null);

// 相关数据信息
let userTaskList = ref();

// 是否展示数据
let isShowList = ref(false);

// 点击跳转详情页
const goDetail = (item) => {
  console.log(item);
  //修改
  router.push({
    name: "taskDetail",
    query: {
      id: item.task_id,
      node_id: item.id,
      is_operate: 1,
    },
  });
};

// let itemRefs = ref([]);
// const setItemRef = (el) => {
//   console.log("测试");
//   if (el) {
//     itemRefs.push(el);
//   }
// };

// onBeforeUpdate(() => {
//   itemRefs.value = [];
// });

// onUpdated(() => {
//   // console.log(setItemRef.value);
//   setItemRef.value.forEach((el) => {
//     console.log(el.scrollLeft);
//     // let item = window.getComputedStyle(el, ":before");
//     // console.log("-----");
//     // console.log(item.backgroundColor);
//     // console.log(item.width);
//     // item.width='50px'
//     // item.backgroundColor = "rgb(100, 128, 0)";
//     // itemRefs.value.push(item);

//     itemRefs.value.push(el);
//   });
// });

// watch(
//   () => itemRefs.value.map((item) => item.clientWidth),
//   (newVal, oldVal) => {
//     // console.log(`checked values changed from ${oldVal} to ${newVal}`);
//   }
// );

// 头部日期
const headerTimeYear = ref("");
const headerTimeDay = ref("");

// 头部查看类型
// const headerType = ref(1);

// 当天日期
// const todayDay = ref(1);

// 日期集合
const days = ref(0);

// 生成日期数组的计算属性
const daysList = computed(() => {
  const result = [];
  for (let i = 1; i <= days.value; i++) {
    result.push(i);
  }
  return result;
});

// 滚轴
let pageTop = ref(0);

// 时间选项集合
// const dayType = reactive([
//   {
//     id: 1,
//     title: "按月查看",
//   },
//   {
//     id: 2,
//     title: "按年查看",
//   },
// ]);

// 当前时间选项
const selectDayType = reactive({
  id: 1,
  title: "按月查看",
});

// 页面首次加载
onMounted(() => {
  switchToday();
  // console.log(dateNum.value.scrollLeft);
  // scrollTop.value = dateNum.value.scrollLeft;
  window.addEventListener("scroll", scrollListenerHandler);
});

// 页面卸载
onUnmounted(() => {
  window.removeEventListener("scroll", scrollListenerHandler);
});

// 获取列表数据
const getList = () => {
  let mouth = ref(0);
  if (headerTimeDay.value <= 9) {
    mouth.value = `0${headerTimeDay.value}`;
  } else {
    mouth.value = headerTimeDay.value;
  }
  proxy
    .$get("datav/get_self_chart", {
      year: headerTimeYear.value,
      month: mouth.value,
    })
    .then((res) => {
      console.log(res.result);
      userTaskList.value = res.result;
      if (userTaskList.value.length != 0) {
        userTaskList.value.forEach((el) => {
          el.isshow = true;
          el.task_list.forEach((k) => {
            k.begin = k.begin_time.split("-")[2];
          });
        });
        isShowList.value = true;
      } else {
        isShowList.value = false;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

const scrollListenerHandler = () => {
  const scrollTop = document.documentElement.scrollTop;
  // console.log("监听");
  // console.log(scrollTop);
  // pageTop.value = scrollTop;
};

// watch(pageTop, (newValue, oldValue) => {
//   console.log("监听到了txt的变化");
//   console.log(newValue, oldValue);
//   // dateNum.style.background = "#1e1e1e";
// });

// 内容区域滚动事件（原有的）
const contentScrollEvent = (e) => {
  console.log("content滚动事件触发", e.target.scrollLeft);
  // 当content滚动时，同步dateContainer的滚动位置
  if (dateContainer.value) {
    dateContainer.value.scrollLeft = e.target.scrollLeft;
  }
};

// 日期容器滚动事件（新增的）
const dateContainerScrollEvent = (e) => {
  console.log("dateContainer滚动事件触发", e.target.scrollLeft);
  // 当dateContainer滚动时，同步content的滚动位置
  if (content.value) {
    content.value.scrollLeft = e.target.scrollLeft;
  }
};

// const workScrollEvent = (e) => {
//   console.log("横向监听到了");
// };

// 处理时间
const nowDate = (now) => {
  const year = now.getFullYear(); //年份
  const month = now.getMonth() + 1; //月份（0-11）
  const date = now.getDate(); //天数（1到31）
  headerTimeYear.value = year;
  headerTimeDay.value = month;
};

// 切换时间 (上月/下月)
const switchData = (step) => {
  // console.log(step);
  headerTimeDay.value = headerTimeDay.value + step;
  if (headerTimeDay.value == 0) {
    headerTimeDay.value = 12;
    headerTimeYear.value = headerTimeYear.value - 1;
  } else if (headerTimeDay.value == 13) {
    headerTimeDay.value = 1;
    headerTimeYear.value = headerTimeYear.value + 1;
  }
  // 获取当月天数 curretMonthDayCount
  const curretMonth = new Date(headerTimeYear.value, headerTimeDay.value, 0);
  const curretMonthDayCount = curretMonth.getDate();
  // console.log(curretMonthDayCount);
  days.value = curretMonthDayCount;

  getList();
};

const switchToday = () => {
  // 获取时间
  const today = new Date();
  // console.log(today);
  // 处理时间
  nowDate(today);
  // 获取当月天数 curretMonthDayCount
  const curretMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  const curretMonthDayCount = curretMonth.getDate();
  days.value = curretMonthDayCount;
  // 获取列表数据
  getList();
};

// 切换列表是否展示
const switchList = (index) => {
  userTaskList[index].isshow = !userTaskList[index].isshow;
};

// // 相关数据信息
// const userTaskList = reactive([
//   {
//     name: "解烁星",
//     num: 2,
//     isshow: true,
//     taskList: [
//       {
//         title: "云一车管",
//         startTime: "2023-4-1",
//         endTime: "2023-5-1",
//         days: 2,
//         begin: 2,
//         id: 1,
//       },
//       {
//         title: "云一工作",
//         startTime: "2023-4-30",
//         endTime: "2023-5-17",
//         days: 1,
//         begin: 3,
//         id: 2,
//       },
//     ],
//   },
//   {
//     name: "于东阳",
//     num: 1,
//     isshow: true,
//     taskList: [
//       {
//         title: "南阳考勤报表",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 12,
//         begin: 1,
//         id: 3,
//       },
//     ],
//   },
//   {
//     name: "李甜",
//     num: 3,
//     isshow: true,
//     taskList: [
//       {
//         title: "南阳考勤报表测试",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 5,
//         begin: 2,
//         id: 4,
//       },
//       {
//         title: "云一车管测试",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 2,
//         begin: 7,
//         id: 5,
//       },
//       {
//         title: "云一工作测试",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 3,
//         begin: 1,
//         id: 6,
//       },
//     ],
//   },
//   {
//     name: "吉慧雯",
//     num: 6,
//     isshow: true,
//     taskList: [
//       {
//         title: "非常忙",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 2,
//         begin: 2,
//         id: 7,
//       },
//       {
//         title: "也不干啥",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 2,
//         begin: 5,
//         id: 8,
//       },
//       {
//         title: "吃饭",
//         startTime: "2023-4-1",
//         endTime: "2023-5-1",
//         days: 1,
//         begin: 28,
//         id: 9,
//       },
//       {
//         title: "睡觉",
//         startTime: "2023-3-1",
//         endTime: "2023-3-5",
//         days: 2,
//         begin: 2,
//         id: 10,
//       },
//       {
//         title: "呼吸",
//         startTime: "2023-3-1",
//         endTime: "2023-5-18",
//         days: 3,
//         begin: 2,
//         id: 11,
//       },
//       {
//         title: "领工资",
//         startTime: "2023-3-1",
//         endTime: "2023-5-1",
//         days: 2,
//         begin: 2,
//         id: 12,
//       },
//     ],
//   },
// ]);
</script>

<style lang="scss" scoped>
.content {
  background: #f2f3f4;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  overflow-x: auto;
  overflow-y: auto;
  white-space: nowrap;
}

.header {
  position: fixed;
  right: 0;
  top: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  width: 100%;
  height: 54px;
  line-height: 54px;
  background-color: #f9f9f9;
  box-sizing: border-box;
  z-index: 999;

  .right {
    display: flex;

    .line {
      margin: 16px 8px;
      width: 1px;
      height: 22px;
      background: #e5e6e8;
    }

    .today {
      span {
        color: #9e9fa1;
      }
    }

    .arrow-down {
      margin-left: 6px;
    }

    .arrow-left {
      margin-left: 12px;
    }

    .arrow-right {
      margin-left: 12px;
    }
  }
}

.dateNum {
  position: fixed;
  left: 0;
  top: 0;
  margin-top: 54px;
  height: 42px;
  line-height: 42px;
  text-align: left;
  color: #747677;
  z-index: 999;
  width: 100%;
  overflow: hidden;

  .dateContainer {
    display: block;
    white-space: nowrap;
    overflow-x: auto;
    width: 100%;
    height: 42px;
    line-height: 42px;
  }

  .day {
    background-color: #fff;
    display: inline-block;
    width: 50px;
    text-align: center;
  }
}

.item {
  display: inline-block;
  background: #fff;
  // width: 15000px;
  // height: 100px;
}

.userName {
  position: sticky;
  left: 0;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding: 0 12px;
  width: 100%;
  height: 44px;
  line-height: 44px;
  box-sizing: border-box;

  .left {
    display: flex;

    .arrow-icon {
      line-height: 44px;
      font-size: 21px;
      transition: all 0.2s;
    }

    .active-arrow {
      transform: rotate(90deg);
    }

    .img {
      margin-left: 12px;
      margin-top: 8px;
      width: 28px;
      height: 28px;
      border-radius: 6px;
      background-color: #3662ec;
      color: #fff;
      line-height: 28px;
      text-align: center;
    }

    .name {
      margin-left: 8px;
    }
  }

  .right {
    color: #a2a3a5;
  }
}

.firstName {
  margin-top: 108px;
}

.taskList {
  position: relative;
  .day {
    background-color: #fff;
    display: inline-block;
    width: 50px;
    // height: 44px;
    text-align: center;
    border-right: 2px solid #f8f8f9;
    box-sizing: border-box;
  }
  .taskBlocks {
    position: absolute;
    top: 6px;
    z-index: 99;
    .taskBlock {
      position: relative;
      margin-top: 12px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;
      background: rgba(0, 176, 66, 0.12);
      text-align: center;
    }

    // .taskBlock::before {
    //   display: block;
    //   content: "";
    //   position: absolute;
    //   top: 0;
    //   width: 32px;
    //   height: 32px;
    //   background-color: green;
    // }

    .arrow-task {
      // position: fixed;
      width: 32px;
      height: 32px;
      background-color: #c5ecd4;
      border-radius: 4px;
      z-index: 99;
    }
  }

  .taskBlock:first-child {
    margin-top: 0;
  }
}
</style>