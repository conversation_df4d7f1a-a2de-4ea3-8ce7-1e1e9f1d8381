import { createApp } from "vue";
import router from "@/router";
import App from "./App.vue";
import { axiosPlugins } from "@/untils/methods";
import { dingPlugins } from "@/untils/dingtalk";
import store from "@/store";
import 'vant/lib/index.css';
import echarts from '@/untils/echarts';

const app = createApp(App);

app.config.globalProperties.$echarts = echarts

app.use(router);
app.use(store);
app.use(axiosPlugins);
app.use(dingPlugins);
app.mount("#app");
