<template>
  <template v-if="workDirectory.length != 0">
    <div class="content">
      <div class="sort" v-for="(el, index) in workDirectory" :key="index">
        <div class="header">
          <span class="sort-name" :style="{ background: el.color }">{{
            el.name
          }}</span>
          <span>{{ el.num }}</span>
        </div>
        <div class="taskList">
          <div
            class="taskItem"
            v-for="(item, x) in el.items"
            :key="x"
            @click="goDetail(item.task_id)"
          >
            <div class="title">{{ item.title }}</div>
            <div class="label"><img src="/icons/text.png" alt="" />标题</div>
            <div class="value">{{ item.title }}</div>
            <div class="label"><img src="/icons/person.png" alt="" />人员</div>
            <div class="value value-img">
              <template v-if="item.avatar">
                <img class="img" :src="item.avatar" alt="" />
              </template>
              <template v-else>
                <div class="img">
                  {{ item.user_name[0] }}
                </div>
              </template>
              <span>{{ item.user_name }}</span>
            </div>
            <div class="label">
              <img src="/icons/date.png" alt="" />开始日期
            </div>
            <div
              class="value"
              :style="{ color: item.begin_time ? '' : '#a2a3a5' }"
            >
              {{ item.begin_time ? item.begin_time : "未选择日期" }}
            </div>
            <div class="label">
              <img src="/icons/date.png" alt="" />结束日期
            </div>
            <div
              class="value"
              :style="{
                color: item.end_time
                  ? item.time_out
                    ? '#ef405b'
                    : ''
                  : '#a2a3a5',
              }"
            >
              {{ item.end_time ? item.end_time : "未选择日期" }}
            </div>
            <div class="label">
              <img src="/icons/status.png" alt="" />任务状态
            </div>
            <div
              class="value value-sort"
              :style="{ background: item.time_out ? '#e6edff' : '#ffeae3' }"
            >
              {{ item.time_out ? "已完成" : "未完成" }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  <template v-else>
    <van-empty
      image="/icons/acquiesce.png"
      image-size="250"
      :description="description"
    />
  </template>
</template>
  
<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
// 接口
const { proxy } = getCurrentInstance();
import { useRouter } from "vue-router";

const router = useRouter();

// 看板数据
let workDirectory = ref([]);
// 背景颜色
const colors = reactive(["#ff713d", "#007fff", "#00ba46", "#45d9d2"]);
let description = ref("暂无内容");

// 页面首次加载
onMounted(() => {
  // 获取列表数据
  getList();
});

// 获取列表数据
const getList = () => {
  proxy
    .$get("datav/get_task_list")
    .then((res) => {
      console.log(res.result);
      // console.log(res.result);
      if (res.errcode == 0) {
        res.result.forEach((el, index) => {
          // console.log(el, index);
          el.color = colors[index % 3];
        });
        workDirectory.value = res.result;
      } else {
        // showFailToast(res.errmsg);
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 点击跳转详情页
const goDetail = (id) => {
  // console.log(id);
  router.push({ name: "TaskAllocationInfo", query: { id: id } });
};

// const workDirectory = reactive([
//   {
//     name: "云一支付",
//     id: 1,
//     color: "#ff713d",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
//   {
//     name: "云一门岗",
//     id: 2,
//     color: "#007fff",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//     ],
//   },
//   {
//     name: "云一车管",
//     id: 3,
//     color: "#00ba46",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
//   {
//     name: "云一发布",
//     id: 4,
//     color: "#45d9d2",
//     num: 2,
//     items: [
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 0,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//       {
//         title: "产品PRD编写",
//         img: "",
//         name: "曼特宁",
//         start_time: "2023-04-19",
//         end_time: "2023-04-28",
//         timeout: 1,
//       },
//     ],
//   },
// ]);
</script>
  
<style lang="scss" scoped>
.content {
  display: flex;
  padding: 16px 0;
  overflow: scroll;

  .sort {
    flex-shrink: 0;
    width: 210px;
    margin-left: 16px;

    .header {
      line-height: 26px;

      span {
        display: inline-block;
      }

      .sort-name {
        margin-right: 8px;
        padding: 0 6px;
        border-radius: 6px;
        color: #fff;
      }
    }

    .taskList {
      margin-top: 23px;

      .taskItem {
        margin-top: 14px;
        padding: 16px;
        border-radius: 8px;
        background-color: #fff;

        .title {
          font-size: 14px;
          font-weight: 550;
          line-height: 22px;
        }

        .label {
          margin-top: 8px;
          color: #a2a3a5;
          line-height: 22px;
          overflow: hidden;

          img {
            margin-right: 4px;
            margin-top: 4px;
            width: 14px;
            height: 14px;
          }
        }

        .value {
          margin-top: 6px;
          font-size: 14px;
        }

        .value-img {
          display: flex;
          line-height: 24px;
          .img {
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 4px;
            margin-right: 6px;
            text-align: center;
            background-color: #457af7;
            color: #fff;
          }
        }
        .value-sort {
          padding: 0 4px;
          height: 22px;
          line-height: 22px;
          display: inline-block;
          border-radius: 4px;
        }
      }
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }
}
</style>