<template>
  <van-popup v-model:show="show" position="bottom">
    <van-date-picker v-model="currentDate" :title="datePickerTitle" @cancel="closeDate" @confirm="changeDate" />
  </van-popup>
  <div class="content" v-if="taskList.length >= 1">
    <template v-for="(el, index) in taskList" :key="index">
      <div class="task-card">
        <div class="header" @click="goDetail(el.task_id, el.id)">
          <div class="title">
            <!-- <img src="/icons/lv1.png" alt="" /> -->
            {{ el.task_title }}
          </div>
          <div class="tag">
            <span v-if="status == 0" :style="{ color: '#959698' }">待验收</span>
            <span v-else-if="status == 1" :style="{ color: '#ff662d' }">待完成</span>
            <span v-else-if="status == 2" :style="{ color: '#00ba46' }">已完成</span>
          </div>
        </div>
        <div class="desc">
          <van-row>
            <van-col span="8" class="verticalline">
              <div class="user">
                <template v-if="el.avatar">
                  <img :src="el.avatar" alt="" />
                </template>
                <template v-else>
                  <div class="img">
                    {{ el.user_name[0] }}
                  </div>
                </template>
                <span>{{ el.user_name }}</span>
              </div>
            </van-col>
            <van-col span="8" class="verticalline">
              <div class="start" v-if="el.begin_time">
                {{ el.begin_time }}
              </div>
              <div v-else>
                <div class="select" @click="showPopup(el)" v-if="el.duration==null">
                  请选择时间
                </div>
                <div class="select" v-else>
                  已报工时：{{ el.duration }}天
                </div>
              </div>
            </van-col>
            <van-col span="8">
              <div class="end" v-if="el.end_time">
                {{ el.end_time }}
              </div>
              <div v-else>
                <div class="select" @click="showPopup(el)" v-if="!el.duration">请选择时间</div>
                <div class="select" v-else>
                  已报工时：{{ el.duration }}天
                </div>
              </div>
            </van-col>
          </van-row>
        </div>
      </div>
    </template>
  </div>
  <div v-else-if="showDesc && taskList.length == 0">
    <van-empty image="/icons/acquiesce.png" image-size="250" :description="description" />
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { useRoute, useRouter } from "vue-router";
const { proxy } = getCurrentInstance();

const route = useRoute();
const router = useRouter();

// 项目列表
let taskList = ref([]);
// 页面状态
let status = ref();

let showDesc = ref(false);
let description = ref("暂无内容");

// 日期星期
const today = ref("");
const week = ref("");

// 日期选择器
const show = ref(false);
const datePickerTitle = ref("选择日期");
// 时间选择器默认展示日期
// const currentDate = ref(["2021", "01", "01"]);
let currentDate = ref([]);

// 储存需要更改任务id
let changedTaskId = ref({
  id: 0,
  string: "",
});

// 是否可以切换日期
let is_operate = ref(0);

onMounted(() => {
  console.log(route.query);
  status.value = route.query.status;
  getTaskList(route.query.status);

  if (route.query.status == 0) {
    is_operate.value = 1;
  } else {
    is_operate.value = 0;
  }

  // 获取当天日期
  const date = new Date();
  const weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  today.value = `${date.getMonth() + 1}月${date.getDate()}日`;
  week.value = weekday[date.getDay()];
  const year = date.getFullYear(); //年份
  const month = date.getMonth() + 1; //月份（0-11）
  const day = date.getDate(); //天数（1到31）
  currentDate.value.push(year);
  currentDate.value.push(month);
  currentDate.value.push(day);
});

// 获取项目列表
const getTaskList = (num) => {
  proxy
    .$get("task/get_my_ls", {
      type: num,
    })
    .then((res) => {
      console.log(res, 9999);
      if (res.errcode == 0) {
        taskList.value = res.result.data;
      } else {
        description.value = res.errmsg;
      }
      showDesc.value = true;
    })
    .catch((err) => {
      console.log(err);
    });
};

// 打开日期选择器
const showPopup = (el) => {
  proxy.$_dd.device.notification.prompt({
    message: "请填写该项目工时（天）",
    title: "工作时长",
    // defaultText: "请输入时长",
    buttonLabels: ["取消", "确定"],
    onSuccess: function (res) {
      if (res.value % 1 !== 0) {
        showFailToast("请输入整数");
        return;
      }
      // 调用成功时回调
      // console.log(res);
      if (res.buttonIndex == 1) {
        proxy
          .$post("task/post_modify_time", {
            id: el.id,
            task_id: el.task_id,
            duration: res.value,
          })
          .then((res) => {
            console.log(res);
            if (res.errcode == 0) {
              showSuccessToast("提交成功");
              getTaskList(status.value);
            } else {
              showFailToast(res.errmsg);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    },
    onFail: function (err) {
      // 调用失败时回调
      // console.log(err);
    },
  });
};

// 关闭日期选择器
const closeDate = () => {
  show.value = false;
};

// 修改日期选择器
const changeDate = (value) => {
  showConfirmDialog({
    title: "请确认选择日期",
    message: `${datePickerTitle.value}为${value.selectedValues.join(
      "-"
    )}，注意：日期一旦确认无法更改`,
  })
    .then(() => {
      // console.log(value.selectedValues);
      const postData = {
        id: changedTaskId.value.id,
      };
      if (changedTaskId.value.string == "begin_time") {
        postData.begin_time = value.selectedValues.join("-");
      } else {
        postData.end_time = value.selectedValues.join("-");
      }
      proxy
        .$post("task/post_modify_time", postData)
        .then((res) => {
          // console.log(res);
          if (res.errcode == 0) {
            showSuccessToast("提交成功");
            getTaskList(status.value);
          } else {
            showFailToast(res.errmsg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
      closeDate();
    })
    .catch(() => {
      // on cancel
    });
};

// 点击跳转详情页
const goDetail = (id, node_id) => {
  console.log(id, node_id, is_operate.value, 555);
  router.push({
    name: "taskDetail",
    query: { id, node_id, is_operate: is_operate.value },
  });
};

// // 任务详情信息
// const taskList = reactive([
//   {
//     title: "新功能流程梳理会议",
//     name: "吉慧雯",
//     status: 0,
//     startTime: "2023-04-19",
//     endTime: "2023-04-28",
//     img: "",
//   },
//   {
//     title: "新功能流程梳理会议",
//     name: "吉慧雯",
//     status: 1,
//     startTime: "2023-04-19",
//     endTime: "2023-04-28",
//   },
//   {
//     title: "新功能流程梳理会议",
//     name: "吉慧雯",
//     status: 2,
//     startTime: "2023-04-19",
//     endTime: "2023-04-28",
//   },
//   {
//     title: "新功能流程梳理会议",
//     name: "吉慧雯",
//     status: 0,
//     startTime: "2023-04-19",
//     endTime: "2023-04-28",
//   },
// ]);
</script>

<style lang="scss" scoped>
.content {
  overflow: hidden;
}

.task-card {
  margin-left: 2%;
  margin-top: 12px;
  padding: 16px 12px;
  width: 96%;
  background-color: #fff;
  border-radius: 8px;
  box-sizing: border-box;

  .header {
    display: flex;
    justify-content: space-between;

    .title {
      display: flex;
      font-size: 17px;
      line-height: 22px;

      img {
        width: 22px;
        height: 22px;
        margin-right: 10px;
      }
    }
  }

  .desc {
    margin-top: 17px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    font-size: 13px;

    .verticalline {
      position: relative;
    }

    .verticalline::after {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: #eaebed;
    }

    .user {
      display: flex;
      align-items: center;

      img {
        display: block;
        width: 28px;
        height: 28px;
        border-radius: 6px;
      }

      .img {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        background-color: rgb(0, 137, 255);
        color: #fff;
        text-align: center;
        line-height: 28px;
      }

      span {
        margin-left: 8px;
      }
    }

    .select {
      color: #a2a3a5;
    }
  }
}
</style>