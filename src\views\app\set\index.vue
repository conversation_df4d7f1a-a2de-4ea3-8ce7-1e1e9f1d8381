<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset class="item">
        <selectDilog
          :label="'分配规则'"
          :locList="priorityList"
          :keyShow="'title'"
          @postData="getPriority"
          :fileValue="{
            label: priorityList[form.mode].title,
            id: form.mode,
          }"
          :rules="[{ required: true, message: '请选择分配规则' }]"
        ></selectDilog>
      </van-cell-group>
      <div class="tips">
        <div class="line">
          饱和模式：当人员有空闲时间时，会尽可能拆分任务并分配
        </div>
        <div class="line">
          专注模式：整个任务会被尽可能地分配给可用人员，以确保任务的高效完成。
        </div>
      </div>
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit"
        >提交</van-button
      >
    </van-action-bar>
  </van-form>
</template>
        
      <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  closeToast,
  showLoadingToast,
} from "vant";
import { useRouter } from "vue-router";
import selectDilog from "../../../components/select.vue";
// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 表格数据
let form = ref({ mode: 0 });
let isLoading = ref(true);

// 循环方式
const priorityList = reactive([
  { id: 0, title: "专注" },
  { id: 1, title: "饱和" },
]);

onMounted(() => {
  // 获取数据详情
  getTask();
});

// 获取详情
const getTask = () => {
  showLoadingToast({
    message: "加载中...",
    forbidClick: true,
  });
  proxy
    .$get("config/get_info")
    .then((res) => {
      console.log(res.result);
      form.value.mode = res.result.mode;
      closeToast();
    })
    .catch((err) => {
      console.log(err);
    });
};

// 获取任务优先级
const getPriority = (item) => {
  // console.log("选择的任务优先级信息 --->", item);
  form.value.mode = item.id;
};

// 提交任务
const submit = () => {
  proxy
    .$post("config/post_modify", form.value)
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("提交成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
        
    <style lang="scss" scoped>
.content {
  padding: 16px 0;
  padding-bottom: 100px;
}

.item {
  margin-top: 16px;
}

.tips {
  margin: 0 5%;
  margin-top: 8px;
  font-size: 12px;
  line-height: 18px;
  color: #9a9c9e;
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>