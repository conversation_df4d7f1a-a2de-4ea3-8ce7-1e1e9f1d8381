<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover, user-scalable=0">
  <meta name="wpk-bid" content="dta_2_132246">
  <title>云一工作</title>
  <script>
    var isDingtalk = navigator && /DingTalk/.test(navigator.userAgent); var isProductEnv = window && window.location && window.location.host
      && window.location.host.indexOf('127.0.0.1') === -1
      && window.location.host.indexOf('localhost') === -1
      && window.location.host.indexOf('192.168.') === -1
    // 如果有其它测试域名，请一起排掉，减少测试环境对生产环境监控的干扰
    if (isProductEnv) { !(function (c, i, e, b) { var h = i.createElement("script"); var f = i.getElementsByTagName("script")[0]; h.type = "text/javascript"; h.crossorigin = true; h.onload = function () { c[b] || (c[b] = new c.wpkReporter({ bid: "dta_2_132246" })); c[b].installAll() }; f.parentNode.insertBefore(h, f); h.src = e })(window, document, "https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js", "__wpk"); }
  </script>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>