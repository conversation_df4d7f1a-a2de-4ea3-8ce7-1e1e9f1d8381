import axios from "axios";
import { start, close } from "@/untils/nprogress";
import router from "@/router";
import { showToast } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { checkApi } from "./methods";

const instance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL,
  timeout: 300000,
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
});
instance.interceptors.request.use(
  (config) => {
    start();
    let token = useLoginStore().token;
    if (checkApi(config)) {
      return config;
    } else if (token) {
      config.headers["Authorization"] = token;
      return config;
    } else {
      router.push("/login");
      // showToast("登录信息过时");
      throw "登录信息过时";
    }
  },
  (err) => Promise.reject(err)
);
instance.interceptors.response.use(
  (res) => {
    close();
    return res.data;
  },
  (err) => Promise.reject(err)
);

export default instance;
