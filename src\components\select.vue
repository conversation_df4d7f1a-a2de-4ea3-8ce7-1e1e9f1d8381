<template>
  <van-field
    v-model="showvalue"
    :label="props.label"
    placeholder="请选择"
    input-align="right"
    is-link
    readonly
    @click="openSelect"
    :rules="props.rules"
  >
    <template #left-icon v-if="props.is_second">
      <img class="second" src="/icons/second.png" alt="" />
    </template>
  </van-field>

  <van-popup
    class="van-popup"
    v-model:show="showBottom"
    position="bottom"
    :style="{ height: '60%' }"
    round
    closeable
    close-icon-position="top-left"
  >
    <div class="header">{{ props.label }}</div>
    <div class="options">
      <van-cell-group v-if="list">
        <template v-for="(el, inx) in list" :key="inx">
          <van-cell @click="selectEl(el)" :title="el[props.keyShow]">
            <template #value>
              <img v-if="id == el.id" src="/icons/select.png" alt="" />
            </template>
          </van-cell>
        </template>
      </van-cell-group>
      <template v-else>
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </div>
  </van-popup>
</template>
    
    <script setup>
import {
  ref,
  reactive,
  onBeforeUpdate,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  showToast,
} from "vant";
import { useRouter } from "vue-router";

// 接口
const { proxy } = getCurrentInstance();

// emit 发送数据
const emit = defineEmits(["postData"]);

let showvalue = ref();

const props = defineProps({
  label: String,
  fileValue: Object,
  keyShow: String,
  is_second: {
    type: Boolean,
    default: false,
  },
  is_remote: {
    type: Boolean,
    default: false,
  },
  locList: Array,
  rules: Array,
  remote: Object,
});

const showBottom = ref(false);

// 打开遮罩层
const openSelect = () => {
  showBottom.value = true;
  if (props.is_remote) {
    // 远程
    getList();
  } else {
    list.value = props.locList;
  }
};

const list = ref();
// 选择id
const id = ref();

onMounted(() => {
  console.log("初始 --->", props);
  if (props.fileValue) {
    showvalue.value = props.fileValue.label;
    id.value = props.fileValue.id;
  }
});

watch(props, (val) => {
  console.log("观察 --->", props, showvalue.value);
  if (props.label == "分配规则") {
    showvalue.value = props.fileValue.label;
    id.value = props.fileValue.id;
  } else {
    if (props.fileValue && !showvalue.value) {
      console.log("走了么");
      showvalue.value = props.fileValue.label;
      id.value = props.fileValue.id;
    }
  }
});

// 状态描述
let description = ref("暂无数据");

// 获取数据列表
const getList = () => {
  proxy
    .$get(props.remote.api, props.remote.postData)
    .then((res) => {
      //   console.log(res);
      if (res.errcode == 0) {
        if (res.result.length != 0) {
          list.value = res.result;
          // if (props.fileValue) {
          //   let selectValue = list.value.find((x) => x.id == props.fileValue.id);
          //   id.value = selectValue[props.keyShow];
          // }
        }
      } else {
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 选择选项
const selectEl = (el) => {
  // console.log("---已选择---", el);
  id.value = el.id;
  showvalue.value = el[props.keyShow];
  showBottom.value = false;

  if (props.is_remote) {
    // 整理传出数据
    let postData = {};
    let rs = props.remote.getData;
    for (const key in rs) {
      if (rs[key].substring(0, 1) == "$") {
        postData[key] = el[rs[key].substring(1)];
      } else {
        postData[key] = rs[key];
      }
    }
    // console.log(postData);
    emit("postData", postData);
  } else {
    emit("postData", el);
  }
};
</script>
    
  <style lang="scss" scoped>
// 二级头
.second {
  width: 24px;
  height: 24px;
}

.van-popup {
  .header {
    position: relative;
    width: 100%;
    height: 52px;
    line-height: 52px;
    text-align: center;
    font-size: 18px;
    border-bottom: 0.5px solid #eaebed;
  }

  img {
    width: 14px;
    height: 10px;
  }
}
</style>