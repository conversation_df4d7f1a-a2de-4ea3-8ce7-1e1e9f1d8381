<template>
  <van-form @submit="submit">
    <div class="content">
      <van-cell-group inset class="item">
        <van-field
          v-model="form.title"
          label="任务名称"
          placeholder="未设置"
          input-align="right"
          is-link
          :rules="[{ required: true, message: '请输入任务名称' }]"
        />
        <van-field
          v-model="form.content"
          label="任务明细"
          placeholder="请输入"
          type="textarea"
          maxlength="50"
          label-align="top"
          rows="2"
          autosize
          show-word-limit
        />
      </van-cell-group>
      <van-cell-group inset class="item">
        <selectDilog
          :label="'任务分组'"
          :is_remote="true"
          :remote="{
            api: 'group/get_all',
            getData: {
              id: '$id',
              title: '$title',
            },
            label: 'title',
          }"
          :keyShow="'title'"
          @postData="getGroupid"
          :rules="[{ required: true, message: '请选择任务分组' }]"
        ></selectDilog>
      </van-cell-group>
      <van-cell-group inset class="item" v-if="!form.immediately">
        <selectDilog
          :label="'任务优先级'"
          :locList="priorityList"
          :keyShow="'title'"
          @postData="getPriority"
          :fileValue="{
            label: priorityList[form.priority - 1].title,
            id: form.priority,
          }"
          :rules="[{ required: true, message: '请选择任务优先级' }]"
        ></selectDilog>
      </van-cell-group>
      <van-cell-group inset class="item">
        <selectDilog
          :label="'任务流或人员'"
          :locList="flowColumn"
          :keyShow="'label'"
          @postData="getFlowColumn"
          :rules="[{ required: true, message: '请选择任务流或人员' }]"
        ></selectDilog>
        <template v-if="form.type == 1">
          <selectDilog
            :label="'任务流'"
            :is_remote="true"
            :remote="{
              api: 'flow/get_all',
              getData: {
                id: '$id',
                title: '$title',
                process: '$process',
              },
              label: 'title',
            }"
            :keyShow="'title'"
            @postData="getFlowid"
          ></selectDilog>
        </template>
        <template v-else-if="form.type == 0">
          <van-field label="人员" readonly @click="headbutton">
            <template #right-icon>
              <van-icon name="add-o" />
            </template>
          </van-field>
          <template v-if="directorLsts">
            <div class="imagestext">
              <template v-for="(el, index) in directorLsts" :key="index">
                <div class="imgItems" @click="delUser(index)">
                  <img :src="el.avatar" alt="" />
                  <van-icon class="del" name="close" />
                </div>
              </template>
            </div>
          </template>
        </template>
      </van-cell-group>
      <van-cell-group inset class="item">
        <uploadMedium
          :label="'任务附件'"
          :multipleChoice="true"
          :isPreview="false"
          @postData="geFiledata"
        ></uploadMedium>
      </van-cell-group>
      <van-cell-group inset class="item">
        <van-cell title="立即执行">
          <template #right-icon>
            <van-switch v-model="form.immediately" />
          </template>
        </van-cell>
      </van-cell-group>
      <van-cell-group inset class="item">
        <van-cell title="新建项目群">
          <template #right-icon>
            <van-switch v-model="form.chat" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <van-action-bar class="bar">
      <van-button class="submit" type="primary" native-type="submit"
        >提交</van-button
      >
    </van-action-bar>
  </van-form>
</template>
      
    <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useLoginStore } from "@/store/dingLogin";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";
import { useRouter } from "vue-router";
import selectDilog from "../../../components/select.vue";
import uploadMedium from "../../../components/upload.vue";
// 路由跳转
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 表格数据
let form = ref({
  title: "",
  flowType: undefined,
  group_title: "",
  group_id: undefined,
  type_title: "",
  flow_title: "",
  flow_id: undefined,
  file: [],
  priority: 1,
});

// 选择人组件
const directorLsts = ref();
const headbutton = () => {
  let selectUsers = ref();
  // console.log(directorLsts.value);

  if (directorLsts.value && directorLsts.value.length > 0) {
    selectUsers = directorLsts.value.map((item) => {
      return item.userid;
    });
  }

  // console.log(selectUsers);
  // console.log("--------------");
  // console.log(useLoginStore().appid);
  proxy.$_dd.biz.contact.complexPicker({
    title: "选择负责人",
    corpId: useLoginStore().corpId,
    multiple: true,
    maxUsers: 10000,
    disabledUsers: [], //不可选用户
    disabledDepartments: [], //不可选部门
    requiredUsers: [], //必选用户（不可取消选中状态）
    requiredDepartments: [], //必选部门（不可取消选中状态）
    pickedUsers: selectUsers, //已选用户
    // pickedDepartments: selectDeparments.value, //已选部门
    appId: useLoginStore().appid, //微应用id
    onSuccess: function (data) {
      // 选择联系人或部门成功后的回调函数
      console.log(data);
      directorLsts.value = data.users.map((item) => {
        return { avatar: item.avatar, name: item.name, userid: item.emplId };
      });
      //   directorLsts.value = data.users.map((item) => item.name).join(",");
      //   selectUsers.value = data.users.map((item) => item.emplId);
    },
    onFail: function (err) {
      // 选择联系人或部门失败后的回调函数
    },
  });
};

// 删除选人
const delUser = (index) => {
  directorLsts.value.splice(index, 1);
};

// 任务流和人员选择
let flowColumn = reactive([
  {
    id: 1,
    label: "人员",
    value: 0,
  },
  {
    id: 2,
    label: "任务流",
    value: 1,
  },
]);

// 循环方式
const priorityList = reactive([
  {
    id: 1,
    title: "一级",
  },
  {
    id: 2,
    title: "二级",
  },
  {
    id: 3,
    title: "三级",
  },
  {
    id: 4,
    title: "四级",
  },
]);

// 获取任务分组
const getGroupid = (item) => {
  console.log("选择的任务分组信息 --->", item);
  form.value.group_id = item.id;
  form.value.group_title = item.title;
};
// 获取任务优先级
const getPriority = (item) => {
  // console.log("选择的任务优先级信息 --->", item);
  form.value.priority = item.id;
};
// 获取任务或人员
const getFlowColumn = (item) => {
  // console.log("选择的任务或人员信息 --->", item);
  form.value.type = item.value;
};
// 获取任务流
const getFlowid = (item) => {
  // console.log("选择的任务流信息 --->", item);
  form.value.flow_id = item.id;
  form.value.flow_title = item.title;
  form.value.userlst = item.process;
};
// 获取文件
const geFiledata = (files) => {
  // console.log("选择的任务流信息 --->", files);
  form.value.file_data = JSON.stringify(files);
};

// 提交任务
const submit = () => {
  let postData = {
    title: form.value.title,
    content: form.value.content,
    group_id: form.value.group_id,
    group_title: form.value.group_title,
    type: form.value.type,
    file_data: form.value.file_data,
    immediately: form.value.immediately ? 1 : 0,
    priority: form.value.priority,
    chat: form.value.chat ? 1 : 0,
  };
  let postUserlst = ref({
    users: directorLsts.value,
    departments: [],
  });
  if (form.value.type == 0) {
    postData.userlst = JSON.stringify(postUserlst.value);
  } else {
    postData.userlst = form.value.userlst;
    postData.flow_id = form.value.flow_id;
    postData.flow_title = form.value.flow_title;
  }

  console.log(form.value, postData);
  proxy
    .$post("task/post_add", postData)
    .then((res) => {
      // console.log(res);
      if (res.errcode == 0) {
        showSuccessToast("提交成功");
        setTimeout(() => {
          closeToast();
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
      
  <style lang="scss" scoped>
.content {
  padding: 16px 0;
  padding-bottom: 100px;
}

.item {
  margin-top: 16px;
}

.imagestext {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;

  .imgItems {
    position: relative;
  }
  img {
    margin-right: 10px;
    width: 45px;
    height: 45px;
    border-radius: 3px;
  }

  img:first-child {
    margin-left: 15px;
  }

  .del {
    position: absolute;
    top: 2px;
    right: 11px;
    font-size: 12px;
    color: #b4b4b4;
  }
}

.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }
}
</style>