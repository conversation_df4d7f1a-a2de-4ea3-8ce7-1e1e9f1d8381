<template>
  <div class="content">
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :finished-text="taskflowList.length != 0 ? '没有更多了' : ''"
      @load="onLoad"
    >
      <template v-if="taskflowList.length >= 1">
        <template v-for="(el, index) in taskflowList" :key="index">
          <div class="sort-item">
            <div class="left">
              <div class="title">{{ el.user_name }}</div>
              <div class="line">
                {{ el.user_name }}
              </div>
            </div>
          </div>
        </template>
      </template>
      <template v-else-if="showDesc && taskflowList.length == 0">
        <van-empty
          image="/icons/acquiesce.png"
          image-size="250"
          :description="description"
        />
      </template>
    </van-list>
  </div>
</template>
        
  <script setup>
import {
  ref,
  reactive,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  showConfirmDialog,
  showSuccessToast,
  showFailToast,
  closeToast,
} from "vant";

// 路由跳转
const route = useRoute();
const router = useRouter();

// 接口
const { proxy } = getCurrentInstance();

// 接口配置数据
let postData = ref({
  page: 0,
  per_page: 10,
  is_all: 0,
});
// 头部 tab 数字提示
let doneNum = ref(0);
// 数据列表
let taskflowList = ref([]);
// 列表总数
let total = ref(0);
// 暂无数据文字描述
let showDesc = ref(false);
let description = ref("暂无任务");
// 列表加载
const loading = ref(false);
const finished = ref(false);

onBeforeMount(() => {});

// 获取任务分配列表
const getList = () => {
  proxy
    .$get("task/get_ls", postData.value)
    .then((res) => {
      // console.log(res.result.data);
      if (postData.value.is_all == 0) {
        doneNum.value = res.result.total;
      }
      res.result.data.forEach((el) => {
        const dates = el.created_at.split(" ");
        const date = dates[0];
        const time = dates[1];
        const dateItems = date.split("-");
        const timeItmes = time.split(":");
        // console.log("---------------");
        // console.log(dateItems, timeItmes);
        el.created_content = `创建于${dateItems[1]}月${dateItems[2]}日 ${timeItmes[0]}:${timeItmes[1]}`;
        taskflowList.value.push(el);
      });
      total.value = res.result.total;
      showDesc.value = true;
      loading.value = false;
      // 加载完毕
      if (taskflowList.value.length >= total.value) {
        finished.value = true;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 列表加载
const onLoad = () => {
  // console.log("需要再次加载数据");
  postData.value.page = postData.value.page + 1;
  getList();
};

// 点击跳转详情
const goDetail = (id) => {
  router.push({ name: "TaskAllocationInfo", query: { id } });
};
</script>
        
  <style lang="scss" scoped>
.content {
  padding-bottom: 75px;
  padding-top: 16px;

  .sort-item {
    display: flex;
    justify-content: space-between;
    margin-left: 5%;
    margin-top: 12px;
    padding: 16px;
    width: 90%;
    // height: 79px;
    border-radius: 6px;
    background-color: #fff;
    box-sizing: border-box;

    .left {
      .line {
        font-size: 13px;
        display: flex;
        color: #a2a3a5;
        line-height: 21px;
      }
    }
  }
}
</style>